use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use sqlx::FromRow;
use uuid::Uuid;

/// Role membership model for SpiceDB synchronization
#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct RoleMembership {
    pub id: Uuid,
    pub role_id: String,
    pub member_id: String,
    pub member_type: String, // "user" or "agent"
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

/// New role membership request
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NewRoleMembership {
    pub role_id: String,
    pub member_id: String,
    pub member_type: String,
}

/// User permission model for SpiceDB synchronization
#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct UserPermission {
    pub id: Uuid,
    pub user_id: String,
    pub resource_type: String,
    pub resource_id: String,
    pub permission: String,
    pub granted_via: String, // "direct" or "role"
    pub granted_via_id: Option<String>, // role_id if granted via role
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

/// New user permission request
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NewUserPermission {
    pub user_id: String,
    pub resource_type: String,
    pub resource_id: String,
    pub permission: String,
    pub granted_via: String,
    pub granted_via_id: Option<String>,
}

/// Agent permission model for SpiceDB synchronization
#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct AgentPermission {
    pub id: Uuid,
    pub agent_id: String,
    pub resource_type: String,
    pub resource_id: String,
    pub permission: String,
    pub granted_via: String, // "direct" or "role"
    pub granted_via_id: Option<String>, // role_id if granted via role
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

/// New agent permission request
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NewAgentPermission {
    pub agent_id: String,
    pub resource_type: String,
    pub resource_id: String,
    pub permission: String,
    pub granted_via: String,
    pub granted_via_id: Option<String>,
}

/// Permission summary for UI display
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PermissionSummary {
    pub entity_id: String,
    pub entity_type: String, // "user" or "agent"
    pub roles: Vec<RoleInfo>,
    pub direct_permissions: Vec<PermissionInfo>,
    pub effective_permissions: Vec<PermissionInfo>,
}

/// Role information for permission summary
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RoleInfo {
    pub role_id: String,
    pub role_name: String,
    pub role_color: Option<String>,
}

/// Permission information for summary
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PermissionInfo {
    pub resource_type: String,
    pub resource_id: String,
    pub permission: String,
    pub granted_via: String,
    pub granted_via_id: Option<String>,
}

/// Role membership with enriched data for UI
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EnrichedRoleMembership {
    pub role_id: String,
    pub role_name: String,
    pub role_description: Option<String>,
    pub role_color: Option<String>,
    pub member_id: String,
    pub member_type: String,
    pub member_name: Option<String>,
    pub member_email: Option<String>,
    pub member_avatar_url: Option<String>,
    pub created_at: String,
}

/// Sync status for monitoring
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SyncStatus {
    pub last_sync: DateTime<Utc>,
    pub role_memberships_count: i64,
    pub user_permissions_count: i64,
    pub agent_permissions_count: i64,
    pub spicedb_healthy: bool,
    pub sync_errors: Vec<String>,
}

/// Sync operation result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SyncResult {
    pub operation: String,
    pub entity_type: String,
    pub entity_id: String,
    pub success: bool,
    pub error: Option<String>,
}

/// Bulk sync request
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BulkSyncRequest {
    pub sync_role_memberships: bool,
    pub sync_user_permissions: bool,
    pub sync_agent_permissions: bool,
    pub force_full_sync: bool,
}

/// Bulk sync response
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BulkSyncResponse {
    pub results: Vec<SyncResult>,
    pub total_operations: usize,
    pub successful_operations: usize,
    pub failed_operations: usize,
    pub duration_ms: u64,
}
