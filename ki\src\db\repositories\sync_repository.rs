use anyhow::Result;
use uuid::Uuid;
use crate::db::{
    connection::{Database, DatabaseConnection},
    models::sync::{
        RoleMembership, NewRoleMembership, UserPermission, NewUserPermission,
        AgentPermission, NewAgentPermission, SyncStatus,
    },
};

/// Repository for synchronization-related database operations
pub struct SyncRepository {
    db: Database,
}

impl SyncRepository {
    pub fn new(db: Database) -> Self {
        Self { db }
    }

    // Role Membership operations
    pub async fn create_role_membership(&self, membership: NewRoleMembership) -> Result<RoleMembership> {
        let id = Uuid::new_v4();

        match &self.db.connection as &DatabaseConnection {
            #[cfg(feature = "sqlite")]
            DatabaseConnection::Sqlite(pool) => {
                sqlx::query(
                    r#"
                    INSERT INTO role_memberships (id, role_id, member_id, member_type)
                    VALUES (?, ?, ?, ?)
                    "#
                )
                .bind(id.to_string())
                .bind(&membership.role_id)
                .bind(&membership.member_id)
                .bind(&membership.member_type)
                .execute(pool)
                .await?;

                // Fetch the created record
                let result = sqlx::query_as::<_, RoleMembership>(
                    "SELECT id, role_id, member_id, member_type, created_at, updated_at FROM role_memberships WHERE id = ?"
                )
                .bind(id.to_string())
                .fetch_one(pool)
                .await?;

                Ok(result)
            }
            #[cfg(feature = "postgres")]
            DatabaseConnection::Postgres(pool) => {
                let result = sqlx::query_as::<_, RoleMembership>(
                    r#"
                    INSERT INTO role_memberships (id, role_id, member_id, member_type)
                    VALUES ($1, $2, $3, $4)
                    RETURNING id, role_id, member_id, member_type, created_at, updated_at
                    "#
                )
                .bind(id)
                .bind(&membership.role_id)
                .bind(&membership.member_id)
                .bind(&membership.member_type)
                .fetch_one(pool)
                .await?;

                Ok(result)
            }
            #[allow(unreachable_patterns)]
            _ => Err(anyhow::anyhow!("Unsupported database connection type")),
        }
    }

    pub async fn delete_role_membership(&self, role_id: &str, member_id: &str, member_type: &str) -> Result<bool> {
        match &self.db.connection as &DatabaseConnection {
            #[cfg(feature = "sqlite")]
            DatabaseConnection::Sqlite(pool) => {
                let result = sqlx::query(
                    "DELETE FROM role_memberships WHERE role_id = ? AND member_id = ? AND member_type = ?"
                )
                .bind(role_id)
                .bind(member_id)
                .bind(member_type)
                .execute(pool)
                .await?;

                Ok(result.rows_affected() > 0)
            }
            #[cfg(feature = "postgres")]
            DatabaseConnection::Postgres(pool) => {
                let result = sqlx::query(
                    "DELETE FROM role_memberships WHERE role_id = $1 AND member_id = $2 AND member_type = $3"
                )
                .bind(role_id)
                .bind(member_id)
                .bind(member_type)
                .execute(pool)
                .await?;

                Ok(result.rows_affected() > 0)
            }
            #[allow(unreachable_patterns)]
            _ => Err(anyhow::anyhow!("Unsupported database connection type")),
        }
    }

    pub async fn get_role_memberships(&self, role_id: &str) -> Result<Vec<RoleMembership>> {
        match &self.db.connection as &DatabaseConnection {
            #[cfg(feature = "sqlite")]
            DatabaseConnection::Sqlite(pool) => {
                let memberships = sqlx::query_as::<_, RoleMembership>(
                    "SELECT id, role_id, member_id, member_type, created_at, updated_at FROM role_memberships WHERE role_id = ?"
                )
                .bind(role_id)
                .fetch_all(pool)
                .await?;

                Ok(memberships)
            }
            #[cfg(feature = "postgres")]
            DatabaseConnection::Postgres(pool) => {
                let memberships = sqlx::query_as::<_, RoleMembership>(
                    "SELECT id, role_id, member_id, member_type, created_at, updated_at FROM role_memberships WHERE role_id = $1"
                )
                .bind(role_id)
                .fetch_all(pool)
                .await?;

                Ok(memberships)
            }
            #[allow(unreachable_patterns)]
            _ => Err(anyhow::anyhow!("Unsupported database connection type")),
        }
    }

    pub async fn get_member_roles(&self, member_id: &str, member_type: &str) -> Result<Vec<RoleMembership>> {
        match &self.db.connection as &DatabaseConnection {
            #[cfg(feature = "sqlite")]
            DatabaseConnection::Sqlite(pool) => {
                let memberships = sqlx::query_as::<_, RoleMembership>(
                    "SELECT id, role_id, member_id, member_type, created_at, updated_at FROM role_memberships WHERE member_id = ? AND member_type = ?"
                )
                .bind(member_id)
                .bind(member_type)
                .fetch_all(pool)
                .await?;

                Ok(memberships)
            }
            #[cfg(feature = "postgres")]
            DatabaseConnection::Postgres(pool) => {
                let memberships = sqlx::query_as::<_, RoleMembership>(
                    "SELECT id, role_id, member_id, member_type, created_at, updated_at FROM role_memberships WHERE member_id = $1 AND member_type = $2"
                )
                .bind(member_id)
                .bind(member_type)
                .fetch_all(pool)
                .await?;

                Ok(memberships)
            }
            #[allow(unreachable_patterns)]
            _ => Err(anyhow::anyhow!("Unsupported database connection type")),
        }
    }

    pub async fn clear_role_memberships(&self, role_id: &str) -> Result<()> {
        match &self.db.connection as &DatabaseConnection {
            #[cfg(feature = "sqlite")]
            DatabaseConnection::Sqlite(pool) => {
                sqlx::query("DELETE FROM role_memberships WHERE role_id = ?")
                    .bind(role_id)
                    .execute(pool)
                    .await?;
            }
            #[cfg(feature = "postgres")]
            DatabaseConnection::Postgres(pool) => {
                sqlx::query("DELETE FROM role_memberships WHERE role_id = $1")
                    .bind(role_id)
                    .execute(pool)
                    .await?;
            }
            #[allow(unreachable_patterns)]
            _ => return Err(anyhow::anyhow!("Unsupported database connection type")),
        }

        Ok(())
    }

    // User Permission operations
    pub async fn create_user_permission(&self, permission: NewUserPermission) -> Result<UserPermission> {
        let id = Uuid::new_v4();
        
        #[cfg(feature = "sqlite")]
        {
            let result = sqlx::query_as!(
                UserPermission,
                r#"
                INSERT INTO user_permissions (id, user_id, resource_type, resource_id, permission, granted_via, granted_via_id)
                VALUES ($1, $2, $3, $4, $5, $6, $7)
                RETURNING id, user_id, resource_type, resource_id, permission, granted_via, granted_via_id, created_at, updated_at
                "#,
                id,
                permission.user_id,
                permission.resource_type,
                permission.resource_id,
                permission.permission,
                permission.granted_via,
                permission.granted_via_id
            )
            .fetch_one(&self.db.pool)
            .await?;
            
            Ok(result)
        }
        
        #[cfg(feature = "postgres")]
        {
            let result = sqlx::query_as!(
                UserPermission,
                r#"
                INSERT INTO user_permissions (id, user_id, resource_type, resource_id, permission, granted_via, granted_via_id)
                VALUES ($1, $2, $3, $4, $5, $6, $7)
                RETURNING id, user_id, resource_type, resource_id, permission, granted_via, granted_via_id, created_at, updated_at
                "#,
                id,
                permission.user_id,
                permission.resource_type,
                permission.resource_id,
                permission.permission,
                permission.granted_via,
                permission.granted_via_id
            )
            .fetch_one(&self.db.pool)
            .await?;
            
            Ok(result)
        }
    }

    pub async fn delete_user_permission(
        &self,
        user_id: &str,
        resource_type: &str,
        resource_id: &str,
        permission: &str,
        granted_via: &str,
        granted_via_id: Option<&str>,
    ) -> Result<bool> {
        #[cfg(feature = "sqlite")]
        {
            let result = sqlx::query!(
                r#"
                DELETE FROM user_permissions 
                WHERE user_id = $1 AND resource_type = $2 AND resource_id = $3 
                AND permission = $4 AND granted_via = $5 
                AND (granted_via_id = $6 OR (granted_via_id IS NULL AND $6 IS NULL))
                "#,
                user_id,
                resource_type,
                resource_id,
                permission,
                granted_via,
                granted_via_id
            )
            .execute(&self.db.pool)
            .await?;
            
            Ok(result.rows_affected() > 0)
        }
        
        #[cfg(feature = "postgres")]
        {
            let result = sqlx::query!(
                r#"
                DELETE FROM user_permissions 
                WHERE user_id = $1 AND resource_type = $2 AND resource_id = $3 
                AND permission = $4 AND granted_via = $5 
                AND (granted_via_id = $6 OR (granted_via_id IS NULL AND $6 IS NULL))
                "#,
                user_id,
                resource_type,
                resource_id,
                permission,
                granted_via,
                granted_via_id
            )
            .execute(&self.db.pool)
            .await?;
            
            Ok(result.rows_affected() > 0)
        }
    }

    pub async fn get_user_permissions(&self, user_id: &str) -> Result<Vec<UserPermission>> {
        #[cfg(feature = "sqlite")]
        {
            let permissions = sqlx::query_as!(
                UserPermission,
                r#"
                SELECT id, user_id, resource_type, resource_id, permission, granted_via, granted_via_id, created_at, updated_at 
                FROM user_permissions WHERE user_id = $1
                "#,
                user_id
            )
            .fetch_all(&self.db.pool)
            .await?;
            
            Ok(permissions)
        }
        
        #[cfg(feature = "postgres")]
        {
            let permissions = sqlx::query_as!(
                UserPermission,
                r#"
                SELECT id, user_id, resource_type, resource_id, permission, granted_via, granted_via_id, created_at, updated_at 
                FROM user_permissions WHERE user_id = $1
                "#,
                user_id
            )
            .fetch_all(&self.db.pool)
            .await?;
            
            Ok(permissions)
        }
    }

    pub async fn clear_user_permissions(&self, user_id: &str) -> Result<()> {
        #[cfg(feature = "sqlite")]
        {
            sqlx::query!(
                "DELETE FROM user_permissions WHERE user_id = $1",
                user_id
            )
            .execute(&self.db.pool)
            .await?;
        }
        
        #[cfg(feature = "postgres")]
        {
            sqlx::query!(
                "DELETE FROM user_permissions WHERE user_id = $1",
                user_id
            )
            .execute(&self.db.pool)
            .await?;
        }
        
        Ok(())
    }

    // Agent Permission operations
    pub async fn create_agent_permission(&self, permission: NewAgentPermission) -> Result<AgentPermission> {
        let id = Uuid::new_v4();

        #[cfg(feature = "sqlite")]
        {
            let result = sqlx::query_as!(
                AgentPermission,
                r#"
                INSERT INTO agent_permissions (id, agent_id, resource_type, resource_id, permission, granted_via, granted_via_id)
                VALUES ($1, $2, $3, $4, $5, $6, $7)
                RETURNING id, agent_id, resource_type, resource_id, permission, granted_via, granted_via_id, created_at, updated_at
                "#,
                id,
                permission.agent_id,
                permission.resource_type,
                permission.resource_id,
                permission.permission,
                permission.granted_via,
                permission.granted_via_id
            )
            .fetch_one(&self.db.pool)
            .await?;

            Ok(result)
        }

        #[cfg(feature = "postgres")]
        {
            let result = sqlx::query_as!(
                AgentPermission,
                r#"
                INSERT INTO agent_permissions (id, agent_id, resource_type, resource_id, permission, granted_via, granted_via_id)
                VALUES ($1, $2, $3, $4, $5, $6, $7)
                RETURNING id, agent_id, resource_type, resource_id, permission, granted_via, granted_via_id, created_at, updated_at
                "#,
                id,
                permission.agent_id,
                permission.resource_type,
                permission.resource_id,
                permission.permission,
                permission.granted_via,
                permission.granted_via_id
            )
            .fetch_one(&self.db.pool)
            .await?;

            Ok(result)
        }
    }

    pub async fn delete_agent_permission(
        &self,
        agent_id: &str,
        resource_type: &str,
        resource_id: &str,
        permission: &str,
        granted_via: &str,
        granted_via_id: Option<&str>,
    ) -> Result<bool> {
        #[cfg(feature = "sqlite")]
        {
            let result = sqlx::query!(
                r#"
                DELETE FROM agent_permissions
                WHERE agent_id = $1 AND resource_type = $2 AND resource_id = $3
                AND permission = $4 AND granted_via = $5
                AND (granted_via_id = $6 OR (granted_via_id IS NULL AND $6 IS NULL))
                "#,
                agent_id,
                resource_type,
                resource_id,
                permission,
                granted_via,
                granted_via_id
            )
            .execute(&self.db.pool)
            .await?;

            Ok(result.rows_affected() > 0)
        }

        #[cfg(feature = "postgres")]
        {
            let result = sqlx::query!(
                r#"
                DELETE FROM agent_permissions
                WHERE agent_id = $1 AND resource_type = $2 AND resource_id = $3
                AND permission = $4 AND granted_via = $5
                AND (granted_via_id = $6 OR (granted_via_id IS NULL AND $6 IS NULL))
                "#,
                agent_id,
                resource_type,
                resource_id,
                permission,
                granted_via,
                granted_via_id
            )
            .execute(&self.db.pool)
            .await?;

            Ok(result.rows_affected() > 0)
        }
    }

    pub async fn get_agent_permissions(&self, agent_id: &str) -> Result<Vec<AgentPermission>> {
        #[cfg(feature = "sqlite")]
        {
            let permissions = sqlx::query_as!(
                AgentPermission,
                r#"
                SELECT id, agent_id, resource_type, resource_id, permission, granted_via, granted_via_id, created_at, updated_at
                FROM agent_permissions WHERE agent_id = $1
                "#,
                agent_id
            )
            .fetch_all(&self.db.pool)
            .await?;

            Ok(permissions)
        }

        #[cfg(feature = "postgres")]
        {
            let permissions = sqlx::query_as!(
                AgentPermission,
                r#"
                SELECT id, agent_id, resource_type, resource_id, permission, granted_via, granted_via_id, created_at, updated_at
                FROM agent_permissions WHERE agent_id = $1
                "#,
                agent_id
            )
            .fetch_all(&self.db.pool)
            .await?;

            Ok(permissions)
        }
    }

    pub async fn clear_agent_permissions(&self, agent_id: &str) -> Result<()> {
        #[cfg(feature = "sqlite")]
        {
            sqlx::query!(
                "DELETE FROM agent_permissions WHERE agent_id = $1",
                agent_id
            )
            .execute(&self.db.pool)
            .await?;
        }

        #[cfg(feature = "postgres")]
        {
            sqlx::query!(
                "DELETE FROM agent_permissions WHERE agent_id = $1",
                agent_id
            )
            .execute(&self.db.pool)
            .await?;
        }

        Ok(())
    }

    // Bulk operations for performance
    pub async fn clear_all_role_permissions(&self, role_id: &str) -> Result<()> {
        #[cfg(feature = "sqlite")]
        {
            sqlx::query!(
                "DELETE FROM user_permissions WHERE granted_via = 'role' AND granted_via_id = $1",
                role_id
            )
            .execute(&self.db.pool)
            .await?;

            sqlx::query!(
                "DELETE FROM agent_permissions WHERE granted_via = 'role' AND granted_via_id = $1",
                role_id
            )
            .execute(&self.db.pool)
            .await?;
        }

        #[cfg(feature = "postgres")]
        {
            sqlx::query!(
                "DELETE FROM user_permissions WHERE granted_via = 'role' AND granted_via_id = $1",
                role_id
            )
            .execute(&self.db.pool)
            .await?;

            sqlx::query!(
                "DELETE FROM agent_permissions WHERE granted_via = 'role' AND granted_via_id = $1",
                role_id
            )
            .execute(&self.db.pool)
            .await?;
        }

        Ok(())
    }

    pub async fn get_sync_status(&self) -> Result<SyncStatus> {
        #[cfg(feature = "sqlite")]
        {
            let role_memberships_count: i64 = sqlx::query_scalar!(
                "SELECT COUNT(*) FROM role_memberships"
            )
            .fetch_one(&self.db.pool)
            .await?;

            let user_permissions_count: i64 = sqlx::query_scalar!(
                "SELECT COUNT(*) FROM user_permissions"
            )
            .fetch_one(&self.db.pool)
            .await?;

            let agent_permissions_count: i64 = sqlx::query_scalar!(
                "SELECT COUNT(*) FROM agent_permissions"
            )
            .fetch_one(&self.db.pool)
            .await?;

            Ok(SyncStatus {
                last_sync: chrono::Utc::now(),
                role_memberships_count,
                user_permissions_count,
                agent_permissions_count,
                spicedb_healthy: true, // This will be updated by the sync service
                sync_errors: vec![],
            })
        }

        #[cfg(feature = "postgres")]
        {
            let role_memberships_count: i64 = sqlx::query_scalar!(
                "SELECT COUNT(*) FROM role_memberships"
            )
            .fetch_one(&self.db.pool)
            .await?;

            let user_permissions_count: i64 = sqlx::query_scalar!(
                "SELECT COUNT(*) FROM user_permissions"
            )
            .fetch_one(&self.db.pool)
            .await?;

            let agent_permissions_count: i64 = sqlx::query_scalar!(
                "SELECT COUNT(*) FROM agent_permissions"
            )
            .fetch_one(&self.db.pool)
            .await?;

            Ok(SyncStatus {
                last_sync: chrono::Utc::now(),
                role_memberships_count,
                user_permissions_count,
                agent_permissions_count,
                spicedb_healthy: true, // This will be updated by the sync service
                sync_errors: vec![],
            })
        }
    }
}
