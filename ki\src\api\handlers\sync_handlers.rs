use axum::{
    extract::State,
    http::Status<PERSON><PERSON>,
    Json,
};
use serde::{Deserialize, Serialize};
use crate::{
    services::sync_service::{SyncService, SyncStatus},
    ServerState,
};

/// Sync status response
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct SyncStatusResponse {
    pub user_count: usize,
    pub agent_count: usize,
    pub role_count: usize,
    pub spicedb_healthy: bool,
    pub last_sync: String,
}

impl From<SyncStatus> for SyncStatusResponse {
    fn from(status: SyncStatus) -> Self {
        Self {
            user_count: status.user_count,
            agent_count: status.agent_count,
            role_count: status.role_count,
            spicedb_healthy: status.spicedb_healthy,
            last_sync: status.last_sync.to_rfc3339(),
        }
    }
}

/// Get synchronization status
pub async fn get_sync_status(
    State(server_state): State<ServerState>,
) -> Result<(StatusC<PERSON>, Json<SyncStatusResponse>), (StatusCode, String)> {
    if let Some(spicedb) = &server_state.spicedb {
        let sync_service = SyncService::new(server_state.db.clone(), spicedb.clone());
        
        match sync_service.get_sync_status().await {
            Ok(status) => Ok((StatusCode::OK, Json(status.into()))),
            Err(e) => Err((StatusCode::INTERNAL_SERVER_ERROR, e.to_string())),
        }
    } else {
        Err((StatusCode::SERVICE_UNAVAILABLE, "SpiceDB service not available".to_string()))
    }
}

/// Trigger manual synchronization
pub async fn trigger_sync(
    State(server_state): State<ServerState>,
) -> Result<(StatusCode, Json<SyncStatusResponse>), (StatusCode, String)> {
    if let Some(spicedb) = &server_state.spicedb {
        let sync_service = SyncService::new(server_state.db.clone(), spicedb.clone());
        
        // Perform synchronization
        match sync_service.sync_all().await {
            Ok(()) => {
                // Return updated status
                match sync_service.get_sync_status().await {
                    Ok(status) => Ok((StatusCode::OK, Json(status.into()))),
                    Err(e) => Err((StatusCode::INTERNAL_SERVER_ERROR, e.to_string())),
                }
            }
            Err(e) => Err((StatusCode::INTERNAL_SERVER_ERROR, e.to_string())),
        }
    } else {
        Err((StatusCode::SERVICE_UNAVAILABLE, "SpiceDB service not available".to_string()))
    }
}

/// Health check for SpiceDB connectivity
pub async fn spicedb_health(
    State(server_state): State<ServerState>,
) -> Result<(StatusCode, Json<serde_json::Value>), (StatusCode, String)> {
    if let Some(spicedb) = &server_state.spicedb {
        let sync_service = SyncService::new(server_state.db.clone(), spicedb.clone());
        
        let healthy = sync_service.health_check().await;
        
        let response = serde_json::json!({
            "healthy": healthy,
            "service": "SpiceDB",
            "timestamp": chrono::Utc::now().to_rfc3339()
        });
        
        if healthy {
            Ok((StatusCode::OK, Json(response)))
        } else {
            Ok((StatusCode::SERVICE_UNAVAILABLE, Json(response)))
        }
    } else {
        let response = serde_json::json!({
            "healthy": false,
            "service": "SpiceDB",
            "error": "Service not configured",
            "timestamp": chrono::Utc::now().to_rfc3339()
        });
        
        Ok((StatusCode::SERVICE_UNAVAILABLE, Json(response)))
    }
}
