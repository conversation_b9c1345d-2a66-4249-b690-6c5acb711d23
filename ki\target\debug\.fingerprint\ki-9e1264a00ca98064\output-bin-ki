{"$message_type":"diagnostic","message":"error returned from database: (code: 1) no such table: role_memberships","code":null,"level":"error","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.8.5\\src\\macros\\mod.rs","byte_start":16503,"byte_end":16574,"line_start":328,"line_end":328,"column_start":9,"column_end":80,"is_primary":true,"text":[{"text":"        $crate::sqlx_macros::expand_query!(source = $query, args = [$($args)*])","highlight_start":9,"highlight_end":80}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.8.5\\src\\macros\\mod.rs","byte_start":16503,"byte_end":16574,"line_start":328,"line_end":328,"column_start":9,"column_end":80,"is_primary":false,"text":[{"text":"        $crate::sqlx_macros::expand_query!(source = $query, args = [$($args)*])","highlight_start":9,"highlight_end":80}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\db\\repositories\\sync_repository.rs","byte_start":2425,"byte_end":2639,"line_start":71,"line_end":76,"column_start":26,"column_end":14,"is_primary":false,"text":[{"text":"            let result = sqlx::query!(","highlight_start":26,"highlight_end":39},{"text":"                \"DELETE FROM role_memberships WHERE role_id = $1 AND member_id = $2 AND member_type = $3\",","highlight_start":1,"highlight_end":107},{"text":"                role_id,","highlight_start":1,"highlight_end":25},{"text":"                member_id,","highlight_start":1,"highlight_end":27},{"text":"                member_type","highlight_start":1,"highlight_end":28},{"text":"            )","highlight_start":1,"highlight_end":14}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"sqlx::query!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.8.5\\src\\macros\\mod.rs","byte_start":15595,"byte_end":15613,"line_start":314,"line_end":314,"column_start":1,"column_end":19,"is_primary":false,"text":[{"text":"macro_rules! query (","highlight_start":1,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::sqlx_macros::expand_query!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-macros-0.8.5\\src\\lib.rs","byte_start":116,"byte_end":170,"line_start":9,"line_end":9,"column_start":1,"column_end":55,"is_primary":false,"text":[{"text":"pub fn expand_query(input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":55}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: error returned from database: (code: 1) no such table: role_memberships\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\db\\repositories\\sync_repository.rs:71:26\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m71\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m            let result = sqlx::query!(\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m __________________________^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m72\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                \"DELETE FROM role_memberships WHERE role_id = $1 AND member_id = $2 AND member_type = $3\",\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m73\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                role_id,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m74\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                member_id,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m75\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                member_type\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m76\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            )\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|_____________^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"error returned from database: (code: 1) no such table: role_memberships","code":null,"level":"error","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.8.5\\src\\macros\\mod.rs","byte_start":26699,"byte_end":26792,"line_start":576,"line_end":576,"column_start":9,"column_end":102,"is_primary":true,"text":[{"text":"        $crate::sqlx_macros::expand_query!(record = $out_struct, source = $query, args = [$($args)*])","highlight_start":9,"highlight_end":102}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.8.5\\src\\macros\\mod.rs","byte_start":26699,"byte_end":26792,"line_start":576,"line_end":576,"column_start":9,"column_end":102,"is_primary":false,"text":[{"text":"        $crate::sqlx_macros::expand_query!(record = $out_struct, source = $query, args = [$($args)*])","highlight_start":9,"highlight_end":102}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\db\\repositories\\sync_repository.rs","byte_start":3357,"byte_end":3570,"line_start":101,"line_end":105,"column_start":31,"column_end":14,"is_primary":false,"text":[{"text":"            let memberships = sqlx::query_as!(","highlight_start":31,"highlight_end":47},{"text":"                RoleMembership,","highlight_start":1,"highlight_end":32},{"text":"                \"SELECT id, role_id, member_id, member_type, created_at, updated_at FROM role_memberships WHERE role_id = $1\",","highlight_start":1,"highlight_end":127},{"text":"                role_id","highlight_start":1,"highlight_end":24},{"text":"            )","highlight_start":1,"highlight_end":14}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"sqlx::query_as!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.8.5\\src\\macros\\mod.rs","byte_start":26477,"byte_end":26498,"line_start":571,"line_end":571,"column_start":1,"column_end":22,"is_primary":false,"text":[{"text":"macro_rules! query_as (","highlight_start":1,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::sqlx_macros::expand_query!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-macros-0.8.5\\src\\lib.rs","byte_start":116,"byte_end":170,"line_start":9,"line_end":9,"column_start":1,"column_end":55,"is_primary":false,"text":[{"text":"pub fn expand_query(input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":55}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: error returned from database: (code: 1) no such table: role_memberships\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\db\\repositories\\sync_repository.rs:101:31\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m101\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m            let memberships = sqlx::query_as!(\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m _______________________________^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m102\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                RoleMembership,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m103\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                \"SELECT id, role_id, member_id, member_type, created_at, updated_at FROM role_memberships WHERE role_id = $1\",\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m104\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                role_id\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m105\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            )\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|_____________^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query_as` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"error returned from database: (code: 1) no such table: role_memberships","code":null,"level":"error","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.8.5\\src\\macros\\mod.rs","byte_start":26699,"byte_end":26792,"line_start":576,"line_end":576,"column_start":9,"column_end":102,"is_primary":true,"text":[{"text":"        $crate::sqlx_macros::expand_query!(record = $out_struct, source = $query, args = [$($args)*])","highlight_start":9,"highlight_end":102}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.8.5\\src\\macros\\mod.rs","byte_start":26699,"byte_end":26792,"line_start":576,"line_end":576,"column_start":9,"column_end":102,"is_primary":false,"text":[{"text":"        $crate::sqlx_macros::expand_query!(record = $out_struct, source = $query, args = [$($args)*])","highlight_start":9,"highlight_end":102}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\db\\repositories\\sync_repository.rs","byte_start":4283,"byte_end":4550,"line_start":129,"line_end":134,"column_start":31,"column_end":14,"is_primary":false,"text":[{"text":"            let memberships = sqlx::query_as!(","highlight_start":31,"highlight_end":47},{"text":"                RoleMembership,","highlight_start":1,"highlight_end":32},{"text":"                \"SELECT id, role_id, member_id, member_type, created_at, updated_at FROM role_memberships WHERE member_id = $1 AND member_type = $2\",","highlight_start":1,"highlight_end":150},{"text":"                member_id,","highlight_start":1,"highlight_end":27},{"text":"                member_type","highlight_start":1,"highlight_end":28},{"text":"            )","highlight_start":1,"highlight_end":14}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"sqlx::query_as!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.8.5\\src\\macros\\mod.rs","byte_start":26477,"byte_end":26498,"line_start":571,"line_end":571,"column_start":1,"column_end":22,"is_primary":false,"text":[{"text":"macro_rules! query_as (","highlight_start":1,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::sqlx_macros::expand_query!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-macros-0.8.5\\src\\lib.rs","byte_start":116,"byte_end":170,"line_start":9,"line_end":9,"column_start":1,"column_end":55,"is_primary":false,"text":[{"text":"pub fn expand_query(input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":55}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: error returned from database: (code: 1) no such table: role_memberships\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\db\\repositories\\sync_repository.rs:129:31\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m129\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m   let memberships = sqlx::query_as!(\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m _________________________^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m130\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m       RoleMembership,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m131\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m       \"SELECT id, role_id, member_id, member_type, created_at, updated_at FROM role_memberships WHERE member_id = $1 AND member_type \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m132\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m       member_id,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m133\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m       member_type\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m134\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m   )\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|_______^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query_as` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"error returned from database: (code: 1) no such table: role_memberships","code":null,"level":"error","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.8.5\\src\\macros\\mod.rs","byte_start":16503,"byte_end":16574,"line_start":328,"line_end":328,"column_start":9,"column_end":80,"is_primary":true,"text":[{"text":"        $crate::sqlx_macros::expand_query!(source = $query, args = [$($args)*])","highlight_start":9,"highlight_end":80}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.8.5\\src\\macros\\mod.rs","byte_start":16503,"byte_end":16574,"line_start":328,"line_end":328,"column_start":9,"column_end":80,"is_primary":false,"text":[{"text":"        $crate::sqlx_macros::expand_query!(source = $query, args = [$($args)*])","highlight_start":9,"highlight_end":80}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\db\\repositories\\sync_repository.rs","byte_start":5267,"byte_end":5385,"line_start":159,"line_end":162,"column_start":13,"column_end":14,"is_primary":false,"text":[{"text":"            sqlx::query!(","highlight_start":13,"highlight_end":26},{"text":"                \"DELETE FROM role_memberships WHERE role_id = $1\",","highlight_start":1,"highlight_end":67},{"text":"                role_id","highlight_start":1,"highlight_end":24},{"text":"            )","highlight_start":1,"highlight_end":14}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"sqlx::query!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.8.5\\src\\macros\\mod.rs","byte_start":15595,"byte_end":15613,"line_start":314,"line_end":314,"column_start":1,"column_end":19,"is_primary":false,"text":[{"text":"macro_rules! query (","highlight_start":1,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::sqlx_macros::expand_query!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-macros-0.8.5\\src\\lib.rs","byte_start":116,"byte_end":170,"line_start":9,"line_end":9,"column_start":1,"column_end":55,"is_primary":false,"text":[{"text":"pub fn expand_query(input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":55}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: error returned from database: (code: 1) no such table: role_memberships\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\db\\repositories\\sync_repository.rs:159:13\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m159\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m/\u001b[0m\u001b[0m \u001b[0m\u001b[0m            sqlx::query!(\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m160\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                \"DELETE FROM role_memberships WHERE role_id = $1\",\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m161\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                role_id\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m162\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            )\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|_____________^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"error returned from database: (code: 1) no such table: user_permissions","code":null,"level":"error","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.8.5\\src\\macros\\mod.rs","byte_start":26699,"byte_end":26792,"line_start":576,"line_end":576,"column_start":9,"column_end":102,"is_primary":true,"text":[{"text":"        $crate::sqlx_macros::expand_query!(record = $out_struct, source = $query, args = [$($args)*])","highlight_start":9,"highlight_end":102}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.8.5\\src\\macros\\mod.rs","byte_start":26699,"byte_end":26792,"line_start":576,"line_end":576,"column_start":9,"column_end":102,"is_primary":false,"text":[{"text":"        $crate::sqlx_macros::expand_query!(record = $out_struct, source = $query, args = [$($args)*])","highlight_start":9,"highlight_end":102}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\db\\repositories\\sync_repository.rs","byte_start":5990,"byte_end":6662,"line_start":186,"line_end":200,"column_start":26,"column_end":14,"is_primary":false,"text":[{"text":"            let result = sqlx::query_as!(","highlight_start":26,"highlight_end":42},{"text":"                UserPermission,","highlight_start":1,"highlight_end":32},{"text":"                r#\"","highlight_start":1,"highlight_end":20},{"text":"                INSERT INTO user_permissions (id, user_id, resource_type, resource_id, permission, granted_via, granted_via_id)","highlight_start":1,"highlight_end":128},{"text":"                VALUES ($1, $2, $3, $4, $5, $6, $7)","highlight_start":1,"highlight_end":52},{"text":"                RETURNING id, user_id, resource_type, resource_id, permission, granted_via, granted_via_id, created_at, updated_at","highlight_start":1,"highlight_end":131},{"text":"                \"#,","highlight_start":1,"highlight_end":20},{"text":"                id,","highlight_start":1,"highlight_end":20},{"text":"                permission.user_id,","highlight_start":1,"highlight_end":36},{"text":"                permission.resource_type,","highlight_start":1,"highlight_end":42},{"text":"                permission.resource_id,","highlight_start":1,"highlight_end":40},{"text":"                permission.permission,","highlight_start":1,"highlight_end":39},{"text":"                permission.granted_via,","highlight_start":1,"highlight_end":40},{"text":"                permission.granted_via_id","highlight_start":1,"highlight_end":42},{"text":"            )","highlight_start":1,"highlight_end":14}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"sqlx::query_as!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.8.5\\src\\macros\\mod.rs","byte_start":26477,"byte_end":26498,"line_start":571,"line_end":571,"column_start":1,"column_end":22,"is_primary":false,"text":[{"text":"macro_rules! query_as (","highlight_start":1,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::sqlx_macros::expand_query!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-macros-0.8.5\\src\\lib.rs","byte_start":116,"byte_end":170,"line_start":9,"line_end":9,"column_start":1,"column_end":55,"is_primary":false,"text":[{"text":"pub fn expand_query(input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":55}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: error returned from database: (code: 1) no such table: user_permissions\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\db\\repositories\\sync_repository.rs:186:26\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m186\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m            let result = sqlx::query_as!(\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m __________________________^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m187\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                UserPermission,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m188\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                r#\"\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m189\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                INSERT INTO user_permissions (id, user_id, resource_type, resource_id, permission, granted_via, granted_via_id)\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m199\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                permission.granted_via_id\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m200\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            )\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|_____________^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query_as` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"error returned from database: (code: 1) no such table: user_permissions","code":null,"level":"error","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.8.5\\src\\macros\\mod.rs","byte_start":16503,"byte_end":16574,"line_start":328,"line_end":328,"column_start":9,"column_end":80,"is_primary":true,"text":[{"text":"        $crate::sqlx_macros::expand_query!(source = $query, args = [$($args)*])","highlight_start":9,"highlight_end":80}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.8.5\\src\\macros\\mod.rs","byte_start":16503,"byte_end":16574,"line_start":328,"line_end":328,"column_start":9,"column_end":80,"is_primary":false,"text":[{"text":"        $crate::sqlx_macros::expand_query!(source = $query, args = [$($args)*])","highlight_start":9,"highlight_end":80}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\db\\repositories\\sync_repository.rs","byte_start":7954,"byte_end":8463,"line_start":242,"line_end":255,"column_start":26,"column_end":14,"is_primary":false,"text":[{"text":"            let result = sqlx::query!(","highlight_start":26,"highlight_end":39},{"text":"                r#\"","highlight_start":1,"highlight_end":20},{"text":"                DELETE FROM user_permissions ","highlight_start":1,"highlight_end":46},{"text":"                WHERE user_id = $1 AND resource_type = $2 AND resource_id = $3 ","highlight_start":1,"highlight_end":80},{"text":"                AND permission = $4 AND granted_via = $5 ","highlight_start":1,"highlight_end":58},{"text":"                AND (granted_via_id = $6 OR (granted_via_id IS NULL AND $6 IS NULL))","highlight_start":1,"highlight_end":85},{"text":"                \"#,","highlight_start":1,"highlight_end":20},{"text":"                user_id,","highlight_start":1,"highlight_end":25},{"text":"                resource_type,","highlight_start":1,"highlight_end":31},{"text":"                resource_id,","highlight_start":1,"highlight_end":29},{"text":"                permission,","highlight_start":1,"highlight_end":28},{"text":"                granted_via,","highlight_start":1,"highlight_end":29},{"text":"                granted_via_id","highlight_start":1,"highlight_end":31},{"text":"            )","highlight_start":1,"highlight_end":14}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"sqlx::query!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.8.5\\src\\macros\\mod.rs","byte_start":15595,"byte_end":15613,"line_start":314,"line_end":314,"column_start":1,"column_end":19,"is_primary":false,"text":[{"text":"macro_rules! query (","highlight_start":1,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::sqlx_macros::expand_query!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-macros-0.8.5\\src\\lib.rs","byte_start":116,"byte_end":170,"line_start":9,"line_end":9,"column_start":1,"column_end":55,"is_primary":false,"text":[{"text":"pub fn expand_query(input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":55}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: error returned from database: (code: 1) no such table: user_permissions\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\db\\repositories\\sync_repository.rs:242:26\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m242\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m            let result = sqlx::query!(\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m __________________________^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m243\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                r#\"\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m244\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                DELETE FROM user_permissions \u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m245\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                WHERE user_id = $1 AND resource_type = $2 AND resource_id = $3 \u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m254\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                granted_via_id\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m255\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            )\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|_____________^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"error returned from database: (code: 1) no such table: user_permissions","code":null,"level":"error","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.8.5\\src\\macros\\mod.rs","byte_start":26699,"byte_end":26792,"line_start":576,"line_end":576,"column_start":9,"column_end":102,"is_primary":true,"text":[{"text":"        $crate::sqlx_macros::expand_query!(record = $out_struct, source = $query, args = [$($args)*])","highlight_start":9,"highlight_end":102}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.8.5\\src\\macros\\mod.rs","byte_start":26699,"byte_end":26792,"line_start":576,"line_end":576,"column_start":9,"column_end":102,"is_primary":false,"text":[{"text":"        $crate::sqlx_macros::expand_query!(record = $out_struct, source = $query, args = [$($args)*])","highlight_start":9,"highlight_end":102}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\db\\repositories\\sync_repository.rs","byte_start":9476,"byte_end":9788,"line_start":288,"line_end":295,"column_start":31,"column_end":14,"is_primary":false,"text":[{"text":"            let permissions = sqlx::query_as!(","highlight_start":31,"highlight_end":47},{"text":"                UserPermission,","highlight_start":1,"highlight_end":32},{"text":"                r#\"","highlight_start":1,"highlight_end":20},{"text":"                SELECT id, user_id, resource_type, resource_id, permission, granted_via, granted_via_id, created_at, updated_at ","highlight_start":1,"highlight_end":129},{"text":"                FROM user_permissions WHERE user_id = $1","highlight_start":1,"highlight_end":57},{"text":"                \"#,","highlight_start":1,"highlight_end":20},{"text":"                user_id","highlight_start":1,"highlight_end":24},{"text":"            )","highlight_start":1,"highlight_end":14}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"sqlx::query_as!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.8.5\\src\\macros\\mod.rs","byte_start":26477,"byte_end":26498,"line_start":571,"line_end":571,"column_start":1,"column_end":22,"is_primary":false,"text":[{"text":"macro_rules! query_as (","highlight_start":1,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::sqlx_macros::expand_query!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-macros-0.8.5\\src\\lib.rs","byte_start":116,"byte_end":170,"line_start":9,"line_end":9,"column_start":1,"column_end":55,"is_primary":false,"text":[{"text":"pub fn expand_query(input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":55}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: error returned from database: (code: 1) no such table: user_permissions\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\db\\repositories\\sync_repository.rs:288:31\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m288\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m            let permissions = sqlx::query_as!(\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m _______________________________^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m289\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                UserPermission,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m290\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                r#\"\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m291\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                SELECT id, user_id, resource_type, resource_id, permission, granted_via, granted_via_id, created_at, updated_at \u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m294\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                user_id\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m295\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            )\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|_____________^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query_as` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"error returned from database: (code: 1) no such table: user_permissions","code":null,"level":"error","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.8.5\\src\\macros\\mod.rs","byte_start":16503,"byte_end":16574,"line_start":328,"line_end":328,"column_start":9,"column_end":80,"is_primary":true,"text":[{"text":"        $crate::sqlx_macros::expand_query!(source = $query, args = [$($args)*])","highlight_start":9,"highlight_end":80}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.8.5\\src\\macros\\mod.rs","byte_start":16503,"byte_end":16574,"line_start":328,"line_end":328,"column_start":9,"column_end":80,"is_primary":false,"text":[{"text":"        $crate::sqlx_macros::expand_query!(source = $query, args = [$($args)*])","highlight_start":9,"highlight_end":80}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\db\\repositories\\sync_repository.rs","byte_start":10550,"byte_end":10668,"line_start":322,"line_end":325,"column_start":13,"column_end":14,"is_primary":false,"text":[{"text":"            sqlx::query!(","highlight_start":13,"highlight_end":26},{"text":"                \"DELETE FROM user_permissions WHERE user_id = $1\",","highlight_start":1,"highlight_end":67},{"text":"                user_id","highlight_start":1,"highlight_end":24},{"text":"            )","highlight_start":1,"highlight_end":14}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"sqlx::query!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.8.5\\src\\macros\\mod.rs","byte_start":15595,"byte_end":15613,"line_start":314,"line_end":314,"column_start":1,"column_end":19,"is_primary":false,"text":[{"text":"macro_rules! query (","highlight_start":1,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::sqlx_macros::expand_query!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-macros-0.8.5\\src\\lib.rs","byte_start":116,"byte_end":170,"line_start":9,"line_end":9,"column_start":1,"column_end":55,"is_primary":false,"text":[{"text":"pub fn expand_query(input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":55}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: error returned from database: (code: 1) no such table: user_permissions\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\db\\repositories\\sync_repository.rs:322:13\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m322\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m/\u001b[0m\u001b[0m \u001b[0m\u001b[0m            sqlx::query!(\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m323\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                \"DELETE FROM user_permissions WHERE user_id = $1\",\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m324\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                user_id\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m325\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            )\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|_____________^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"error returned from database: (code: 1) no such table: agent_permissions","code":null,"level":"error","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.8.5\\src\\macros\\mod.rs","byte_start":26699,"byte_end":26792,"line_start":576,"line_end":576,"column_start":9,"column_end":102,"is_primary":true,"text":[{"text":"        $crate::sqlx_macros::expand_query!(record = $out_struct, source = $query, args = [$($args)*])","highlight_start":9,"highlight_end":102}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.8.5\\src\\macros\\mod.rs","byte_start":26699,"byte_end":26792,"line_start":576,"line_end":576,"column_start":9,"column_end":102,"is_primary":false,"text":[{"text":"        $crate::sqlx_macros::expand_query!(record = $out_struct, source = $query, args = [$($args)*])","highlight_start":9,"highlight_end":102}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\db\\repositories\\sync_repository.rs","byte_start":11269,"byte_end":11946,"line_start":349,"line_end":363,"column_start":26,"column_end":14,"is_primary":false,"text":[{"text":"            let result = sqlx::query_as!(","highlight_start":26,"highlight_end":42},{"text":"                AgentPermission,","highlight_start":1,"highlight_end":33},{"text":"                r#\"","highlight_start":1,"highlight_end":20},{"text":"                INSERT INTO agent_permissions (id, agent_id, resource_type, resource_id, permission, granted_via, granted_via_id)","highlight_start":1,"highlight_end":130},{"text":"                VALUES ($1, $2, $3, $4, $5, $6, $7)","highlight_start":1,"highlight_end":52},{"text":"                RETURNING id, agent_id, resource_type, resource_id, permission, granted_via, granted_via_id, created_at, updated_at","highlight_start":1,"highlight_end":132},{"text":"                \"#,","highlight_start":1,"highlight_end":20},{"text":"                id,","highlight_start":1,"highlight_end":20},{"text":"                permission.agent_id,","highlight_start":1,"highlight_end":37},{"text":"                permission.resource_type,","highlight_start":1,"highlight_end":42},{"text":"                permission.resource_id,","highlight_start":1,"highlight_end":40},{"text":"                permission.permission,","highlight_start":1,"highlight_end":39},{"text":"                permission.granted_via,","highlight_start":1,"highlight_end":40},{"text":"                permission.granted_via_id","highlight_start":1,"highlight_end":42},{"text":"            )","highlight_start":1,"highlight_end":14}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"sqlx::query_as!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.8.5\\src\\macros\\mod.rs","byte_start":26477,"byte_end":26498,"line_start":571,"line_end":571,"column_start":1,"column_end":22,"is_primary":false,"text":[{"text":"macro_rules! query_as (","highlight_start":1,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::sqlx_macros::expand_query!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-macros-0.8.5\\src\\lib.rs","byte_start":116,"byte_end":170,"line_start":9,"line_end":9,"column_start":1,"column_end":55,"is_primary":false,"text":[{"text":"pub fn expand_query(input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":55}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: error returned from database: (code: 1) no such table: agent_permissions\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\db\\repositories\\sync_repository.rs:349:26\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m349\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m            let result = sqlx::query_as!(\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m __________________________^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m350\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                AgentPermission,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m351\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                r#\"\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m352\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                INSERT INTO agent_permissions (id, agent_id, resource_type, resource_id, permission, granted_via, granted_via_id)\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m362\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                permission.granted_via_id\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m363\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            )\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|_____________^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query_as` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"error returned from database: (code: 1) no such table: agent_permissions","code":null,"level":"error","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.8.5\\src\\macros\\mod.rs","byte_start":16503,"byte_end":16574,"line_start":328,"line_end":328,"column_start":9,"column_end":80,"is_primary":true,"text":[{"text":"        $crate::sqlx_macros::expand_query!(source = $query, args = [$($args)*])","highlight_start":9,"highlight_end":80}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.8.5\\src\\macros\\mod.rs","byte_start":16503,"byte_end":16574,"line_start":328,"line_end":328,"column_start":9,"column_end":80,"is_primary":false,"text":[{"text":"        $crate::sqlx_macros::expand_query!(source = $query, args = [$($args)*])","highlight_start":9,"highlight_end":80}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\db\\repositories\\sync_repository.rs","byte_start":13213,"byte_end":13722,"line_start":405,"line_end":418,"column_start":26,"column_end":14,"is_primary":false,"text":[{"text":"            let result = sqlx::query!(","highlight_start":26,"highlight_end":39},{"text":"                r#\"","highlight_start":1,"highlight_end":20},{"text":"                DELETE FROM agent_permissions","highlight_start":1,"highlight_end":46},{"text":"                WHERE agent_id = $1 AND resource_type = $2 AND resource_id = $3","highlight_start":1,"highlight_end":80},{"text":"                AND permission = $4 AND granted_via = $5","highlight_start":1,"highlight_end":57},{"text":"                AND (granted_via_id = $6 OR (granted_via_id IS NULL AND $6 IS NULL))","highlight_start":1,"highlight_end":85},{"text":"                \"#,","highlight_start":1,"highlight_end":20},{"text":"                agent_id,","highlight_start":1,"highlight_end":26},{"text":"                resource_type,","highlight_start":1,"highlight_end":31},{"text":"                resource_id,","highlight_start":1,"highlight_end":29},{"text":"                permission,","highlight_start":1,"highlight_end":28},{"text":"                granted_via,","highlight_start":1,"highlight_end":29},{"text":"                granted_via_id","highlight_start":1,"highlight_end":31},{"text":"            )","highlight_start":1,"highlight_end":14}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"sqlx::query!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.8.5\\src\\macros\\mod.rs","byte_start":15595,"byte_end":15613,"line_start":314,"line_end":314,"column_start":1,"column_end":19,"is_primary":false,"text":[{"text":"macro_rules! query (","highlight_start":1,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::sqlx_macros::expand_query!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-macros-0.8.5\\src\\lib.rs","byte_start":116,"byte_end":170,"line_start":9,"line_end":9,"column_start":1,"column_end":55,"is_primary":false,"text":[{"text":"pub fn expand_query(input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":55}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: error returned from database: (code: 1) no such table: agent_permissions\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\db\\repositories\\sync_repository.rs:405:26\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m405\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m            let result = sqlx::query!(\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m __________________________^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m406\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                r#\"\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m407\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                DELETE FROM agent_permissions\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m408\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                WHERE agent_id = $1 AND resource_type = $2 AND resource_id = $3\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m417\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                granted_via_id\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m418\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            )\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|_____________^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"error returned from database: (code: 1) no such table: agent_permissions","code":null,"level":"error","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.8.5\\src\\macros\\mod.rs","byte_start":26699,"byte_end":26792,"line_start":576,"line_end":576,"column_start":9,"column_end":102,"is_primary":true,"text":[{"text":"        $crate::sqlx_macros::expand_query!(record = $out_struct, source = $query, args = [$($args)*])","highlight_start":9,"highlight_end":102}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.8.5\\src\\macros\\mod.rs","byte_start":26699,"byte_end":26792,"line_start":576,"line_end":576,"column_start":9,"column_end":102,"is_primary":false,"text":[{"text":"        $crate::sqlx_macros::expand_query!(record = $out_struct, source = $query, args = [$($args)*])","highlight_start":9,"highlight_end":102}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\db\\repositories\\sync_repository.rs","byte_start":14706,"byte_end":15022,"line_start":451,"line_end":458,"column_start":31,"column_end":14,"is_primary":false,"text":[{"text":"            let permissions = sqlx::query_as!(","highlight_start":31,"highlight_end":47},{"text":"                AgentPermission,","highlight_start":1,"highlight_end":33},{"text":"                r#\"","highlight_start":1,"highlight_end":20},{"text":"                SELECT id, agent_id, resource_type, resource_id, permission, granted_via, granted_via_id, created_at, updated_at","highlight_start":1,"highlight_end":129},{"text":"                FROM agent_permissions WHERE agent_id = $1","highlight_start":1,"highlight_end":59},{"text":"                \"#,","highlight_start":1,"highlight_end":20},{"text":"                agent_id","highlight_start":1,"highlight_end":25},{"text":"            )","highlight_start":1,"highlight_end":14}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"sqlx::query_as!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.8.5\\src\\macros\\mod.rs","byte_start":26477,"byte_end":26498,"line_start":571,"line_end":571,"column_start":1,"column_end":22,"is_primary":false,"text":[{"text":"macro_rules! query_as (","highlight_start":1,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::sqlx_macros::expand_query!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-macros-0.8.5\\src\\lib.rs","byte_start":116,"byte_end":170,"line_start":9,"line_end":9,"column_start":1,"column_end":55,"is_primary":false,"text":[{"text":"pub fn expand_query(input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":55}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: error returned from database: (code: 1) no such table: agent_permissions\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\db\\repositories\\sync_repository.rs:451:31\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m451\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m            let permissions = sqlx::query_as!(\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m _______________________________^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m452\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                AgentPermission,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m453\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                r#\"\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m454\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                SELECT id, agent_id, resource_type, resource_id, permission, granted_via, granted_via_id, created_at, updated_at\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m457\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                agent_id\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m458\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            )\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|_____________^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query_as` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"error returned from database: (code: 1) no such table: agent_permissions","code":null,"level":"error","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.8.5\\src\\macros\\mod.rs","byte_start":16503,"byte_end":16574,"line_start":328,"line_end":328,"column_start":9,"column_end":80,"is_primary":true,"text":[{"text":"        $crate::sqlx_macros::expand_query!(source = $query, args = [$($args)*])","highlight_start":9,"highlight_end":80}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.8.5\\src\\macros\\mod.rs","byte_start":16503,"byte_end":16574,"line_start":328,"line_end":328,"column_start":9,"column_end":80,"is_primary":false,"text":[{"text":"        $crate::sqlx_macros::expand_query!(source = $query, args = [$($args)*])","highlight_start":9,"highlight_end":80}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\db\\repositories\\sync_repository.rs","byte_start":15758,"byte_end":15879,"line_start":485,"line_end":488,"column_start":13,"column_end":14,"is_primary":false,"text":[{"text":"            sqlx::query!(","highlight_start":13,"highlight_end":26},{"text":"                \"DELETE FROM agent_permissions WHERE agent_id = $1\",","highlight_start":1,"highlight_end":69},{"text":"                agent_id","highlight_start":1,"highlight_end":25},{"text":"            )","highlight_start":1,"highlight_end":14}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"sqlx::query!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.8.5\\src\\macros\\mod.rs","byte_start":15595,"byte_end":15613,"line_start":314,"line_end":314,"column_start":1,"column_end":19,"is_primary":false,"text":[{"text":"macro_rules! query (","highlight_start":1,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::sqlx_macros::expand_query!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-macros-0.8.5\\src\\lib.rs","byte_start":116,"byte_end":170,"line_start":9,"line_end":9,"column_start":1,"column_end":55,"is_primary":false,"text":[{"text":"pub fn expand_query(input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":55}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: error returned from database: (code: 1) no such table: agent_permissions\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\db\\repositories\\sync_repository.rs:485:13\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m485\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m/\u001b[0m\u001b[0m \u001b[0m\u001b[0m            sqlx::query!(\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m486\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                \"DELETE FROM agent_permissions WHERE agent_id = $1\",\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m487\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                agent_id\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m488\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            )\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|_____________^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"error returned from database: (code: 1) no such table: user_permissions","code":null,"level":"error","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.8.5\\src\\macros\\mod.rs","byte_start":16503,"byte_end":16574,"line_start":328,"line_end":328,"column_start":9,"column_end":80,"is_primary":true,"text":[{"text":"        $crate::sqlx_macros::expand_query!(source = $query, args = [$($args)*])","highlight_start":9,"highlight_end":80}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.8.5\\src\\macros\\mod.rs","byte_start":16503,"byte_end":16574,"line_start":328,"line_end":328,"column_start":9,"column_end":80,"is_primary":false,"text":[{"text":"        $crate::sqlx_macros::expand_query!(source = $query, args = [$($args)*])","highlight_start":9,"highlight_end":80}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\db\\repositories\\sync_repository.rs","byte_start":16397,"byte_end":16547,"line_start":510,"line_end":513,"column_start":13,"column_end":14,"is_primary":false,"text":[{"text":"            sqlx::query!(","highlight_start":13,"highlight_end":26},{"text":"                \"DELETE FROM user_permissions WHERE granted_via = 'role' AND granted_via_id = $1\",","highlight_start":1,"highlight_end":99},{"text":"                role_id","highlight_start":1,"highlight_end":24},{"text":"            )","highlight_start":1,"highlight_end":14}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"sqlx::query!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.8.5\\src\\macros\\mod.rs","byte_start":15595,"byte_end":15613,"line_start":314,"line_end":314,"column_start":1,"column_end":19,"is_primary":false,"text":[{"text":"macro_rules! query (","highlight_start":1,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::sqlx_macros::expand_query!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-macros-0.8.5\\src\\lib.rs","byte_start":116,"byte_end":170,"line_start":9,"line_end":9,"column_start":1,"column_end":55,"is_primary":false,"text":[{"text":"pub fn expand_query(input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":55}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: error returned from database: (code: 1) no such table: user_permissions\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\db\\repositories\\sync_repository.rs:510:13\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m510\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m/\u001b[0m\u001b[0m \u001b[0m\u001b[0m            sqlx::query!(\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m511\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                \"DELETE FROM user_permissions WHERE granted_via = 'role' AND granted_via_id = $1\",\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m512\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                role_id\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m513\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            )\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|_____________^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"error returned from database: (code: 1) no such table: agent_permissions","code":null,"level":"error","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.8.5\\src\\macros\\mod.rs","byte_start":16503,"byte_end":16574,"line_start":328,"line_end":328,"column_start":9,"column_end":80,"is_primary":true,"text":[{"text":"        $crate::sqlx_macros::expand_query!(source = $query, args = [$($args)*])","highlight_start":9,"highlight_end":80}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.8.5\\src\\macros\\mod.rs","byte_start":16503,"byte_end":16574,"line_start":328,"line_end":328,"column_start":9,"column_end":80,"is_primary":false,"text":[{"text":"        $crate::sqlx_macros::expand_query!(source = $query, args = [$($args)*])","highlight_start":9,"highlight_end":80}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\db\\repositories\\sync_repository.rs","byte_start":16618,"byte_end":16769,"line_start":517,"line_end":520,"column_start":13,"column_end":14,"is_primary":false,"text":[{"text":"            sqlx::query!(","highlight_start":13,"highlight_end":26},{"text":"                \"DELETE FROM agent_permissions WHERE granted_via = 'role' AND granted_via_id = $1\",","highlight_start":1,"highlight_end":100},{"text":"                role_id","highlight_start":1,"highlight_end":24},{"text":"            )","highlight_start":1,"highlight_end":14}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"sqlx::query!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.8.5\\src\\macros\\mod.rs","byte_start":15595,"byte_end":15613,"line_start":314,"line_end":314,"column_start":1,"column_end":19,"is_primary":false,"text":[{"text":"macro_rules! query (","highlight_start":1,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::sqlx_macros::expand_query!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-macros-0.8.5\\src\\lib.rs","byte_start":116,"byte_end":170,"line_start":9,"line_end":9,"column_start":1,"column_end":55,"is_primary":false,"text":[{"text":"pub fn expand_query(input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":55}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: error returned from database: (code: 1) no such table: agent_permissions\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\db\\repositories\\sync_repository.rs:517:13\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m517\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m/\u001b[0m\u001b[0m \u001b[0m\u001b[0m            sqlx::query!(\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m518\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                \"DELETE FROM agent_permissions WHERE granted_via = 'role' AND granted_via_id = $1\",\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m519\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                role_id\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m520\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            )\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|_____________^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"error returned from database: (code: 1) no such table: role_memberships","code":null,"level":"error","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.8.5\\src\\macros\\mod.rs","byte_start":30656,"byte_end":30719,"line_start":671,"line_end":671,"column_start":9,"column_end":72,"is_primary":true,"text":[{"text":"        $crate::sqlx_macros::expand_query!(scalar = _, source = $query)","highlight_start":9,"highlight_end":72}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.8.5\\src\\macros\\mod.rs","byte_start":30656,"byte_end":30719,"line_start":671,"line_end":671,"column_start":9,"column_end":72,"is_primary":false,"text":[{"text":"        $crate::sqlx_macros::expand_query!(scalar = _, source = $query)","highlight_start":9,"highlight_end":72}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\db\\repositories\\sync_repository.rs","byte_start":17515,"byte_end":17605,"line_start":548,"line_end":550,"column_start":47,"column_end":14,"is_primary":false,"text":[{"text":"            let role_memberships_count: i64 = sqlx::query_scalar!(","highlight_start":47,"highlight_end":67},{"text":"                \"SELECT COUNT(*) FROM role_memberships\"","highlight_start":1,"highlight_end":56},{"text":"            )","highlight_start":1,"highlight_end":14}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"sqlx::query_scalar!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.8.5\\src\\macros\\mod.rs","byte_start":30597,"byte_end":30622,"line_start":669,"line_end":669,"column_start":1,"column_end":26,"is_primary":false,"text":[{"text":"macro_rules! query_scalar (","highlight_start":1,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::sqlx_macros::expand_query!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-macros-0.8.5\\src\\lib.rs","byte_start":116,"byte_end":170,"line_start":9,"line_end":9,"column_start":1,"column_end":55,"is_primary":false,"text":[{"text":"pub fn expand_query(input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":55}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: error returned from database: (code: 1) no such table: role_memberships\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\db\\repositories\\sync_repository.rs:548:47\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m548\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m            let role_memberships_count: i64 = sqlx::query_scalar!(\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m _______________________________________________^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m549\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                \"SELECT COUNT(*) FROM role_memberships\"\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m550\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            )\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|_____________^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query_scalar` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"error returned from database: (code: 1) no such table: user_permissions","code":null,"level":"error","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.8.5\\src\\macros\\mod.rs","byte_start":30656,"byte_end":30719,"line_start":671,"line_end":671,"column_start":9,"column_end":72,"is_primary":true,"text":[{"text":"        $crate::sqlx_macros::expand_query!(scalar = _, source = $query)","highlight_start":9,"highlight_end":72}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.8.5\\src\\macros\\mod.rs","byte_start":30656,"byte_end":30719,"line_start":671,"line_end":671,"column_start":9,"column_end":72,"is_primary":false,"text":[{"text":"        $crate::sqlx_macros::expand_query!(scalar = _, source = $query)","highlight_start":9,"highlight_end":72}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\db\\repositories\\sync_repository.rs","byte_start":17712,"byte_end":17802,"line_start":554,"line_end":556,"column_start":47,"column_end":14,"is_primary":false,"text":[{"text":"            let user_permissions_count: i64 = sqlx::query_scalar!(","highlight_start":47,"highlight_end":67},{"text":"                \"SELECT COUNT(*) FROM user_permissions\"","highlight_start":1,"highlight_end":56},{"text":"            )","highlight_start":1,"highlight_end":14}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"sqlx::query_scalar!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.8.5\\src\\macros\\mod.rs","byte_start":30597,"byte_end":30622,"line_start":669,"line_end":669,"column_start":1,"column_end":26,"is_primary":false,"text":[{"text":"macro_rules! query_scalar (","highlight_start":1,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::sqlx_macros::expand_query!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-macros-0.8.5\\src\\lib.rs","byte_start":116,"byte_end":170,"line_start":9,"line_end":9,"column_start":1,"column_end":55,"is_primary":false,"text":[{"text":"pub fn expand_query(input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":55}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: error returned from database: (code: 1) no such table: user_permissions\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\db\\repositories\\sync_repository.rs:554:47\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m554\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m            let user_permissions_count: i64 = sqlx::query_scalar!(\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m _______________________________________________^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m555\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                \"SELECT COUNT(*) FROM user_permissions\"\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m556\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            )\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|_____________^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query_scalar` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"error returned from database: (code: 1) no such table: agent_permissions","code":null,"level":"error","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.8.5\\src\\macros\\mod.rs","byte_start":30656,"byte_end":30719,"line_start":671,"line_end":671,"column_start":9,"column_end":72,"is_primary":true,"text":[{"text":"        $crate::sqlx_macros::expand_query!(scalar = _, source = $query)","highlight_start":9,"highlight_end":72}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.8.5\\src\\macros\\mod.rs","byte_start":30656,"byte_end":30719,"line_start":671,"line_end":671,"column_start":9,"column_end":72,"is_primary":false,"text":[{"text":"        $crate::sqlx_macros::expand_query!(scalar = _, source = $query)","highlight_start":9,"highlight_end":72}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\db\\repositories\\sync_repository.rs","byte_start":17910,"byte_end":18001,"line_start":560,"line_end":562,"column_start":48,"column_end":14,"is_primary":false,"text":[{"text":"            let agent_permissions_count: i64 = sqlx::query_scalar!(","highlight_start":48,"highlight_end":68},{"text":"                \"SELECT COUNT(*) FROM agent_permissions\"","highlight_start":1,"highlight_end":57},{"text":"            )","highlight_start":1,"highlight_end":14}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"sqlx::query_scalar!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.8.5\\src\\macros\\mod.rs","byte_start":30597,"byte_end":30622,"line_start":669,"line_end":669,"column_start":1,"column_end":26,"is_primary":false,"text":[{"text":"macro_rules! query_scalar (","highlight_start":1,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::sqlx_macros::expand_query!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-macros-0.8.5\\src\\lib.rs","byte_start":116,"byte_end":170,"line_start":9,"line_end":9,"column_start":1,"column_end":55,"is_primary":false,"text":[{"text":"pub fn expand_query(input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":55}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: error returned from database: (code: 1) no such table: agent_permissions\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\db\\repositories\\sync_repository.rs:560:48\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m560\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m            let agent_permissions_count: i64 = sqlx::query_scalar!(\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m ________________________________________________^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m561\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                \"SELECT COUNT(*) FROM agent_permissions\"\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m562\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            )\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|_____________^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query_scalar` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"failed to resolve: could not find `RoleRepository` in `repositories`","code":{"code":"E0433","explanation":"An undeclared crate, module, or type was used.\n\nErroneous code example:\n\n```compile_fail,E0433\nlet map = HashMap::new();\n// error: failed to resolve: use of undeclared type `HashMap`\n```\n\nPlease verify you didn't misspell the type/module's name or that you didn't\nforget to import it:\n\n```\nuse std::collections::HashMap; // HashMap has been imported.\nlet map: HashMap<u32, u32> = HashMap::new(); // So it can be used!\n```\n\nIf you've expected to use a crate name:\n\n```compile_fail\nuse ferris_wheel::BigO;\n// error: failed to resolve: use of undeclared module or unlinked crate\n```\n\nMake sure the crate has been added as a dependency in `Cargo.toml`.\n\nTo use a module from your current crate, add the `crate::` prefix to the path.\n"},"level":"error","spans":[{"file_name":"src\\api\\handlers\\sync_handlers.rs","byte_start":11283,"byte_end":11297,"line_start":303,"line_end":303,"column_start":46,"column_end":60,"is_primary":true,"text":[{"text":"    let role_repo = crate::db::repositories::RoleRepository::new(server_state.db.clone());","highlight_start":46,"highlight_end":60}],"label":"could not find `RoleRepository` in `repositories`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"a struct with a similar name exists","code":null,"level":"help","spans":[{"file_name":"src\\api\\handlers\\sync_handlers.rs","byte_start":11283,"byte_end":11297,"line_start":303,"line_end":303,"column_start":46,"column_end":60,"is_primary":true,"text":[{"text":"    let role_repo = crate::db::repositories::RoleRepository::new(server_state.db.clone());","highlight_start":46,"highlight_end":60}],"label":null,"suggested_replacement":"SyncRepository","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null},{"message":"consider importing this struct","code":null,"level":"help","spans":[{"file_name":"src\\api\\handlers\\sync_handlers.rs","byte_start":0,"byte_end":0,"line_start":1,"line_end":1,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use axum::{","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"use crate::db::repositories::role_repository::RoleRepository;\n","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null},{"message":"if you import `RoleRepository`, refer to it directly","code":null,"level":"help","spans":[{"file_name":"src\\api\\handlers\\sync_handlers.rs","byte_start":11258,"byte_end":11283,"line_start":303,"line_end":303,"column_start":21,"column_end":46,"is_primary":true,"text":[{"text":"    let role_repo = crate::db::repositories::RoleRepository::new(server_state.db.clone());","highlight_start":21,"highlight_end":46}],"label":null,"suggested_replacement":"","suggestion_applicability":"Unspecified","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0433]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: failed to resolve: could not find `RoleRepository` in `repositories`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\api\\handlers\\sync_handlers.rs:303:46\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m303\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let role_repo = crate::db::repositories::RoleRepository::new(server_state.db.clone());\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                              \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mcould not find `RoleRepository` in `repositories`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: a struct with a similar name exists\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m303\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m    let role_repo = crate::db::repositories::\u001b[0m\u001b[0m\u001b[38;5;9mRoleRepository\u001b[0m\u001b[0m::new(server_state.db.clone());\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m303\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m    let role_repo = crate::db::repositories::\u001b[0m\u001b[0m\u001b[38;5;10mSyncRepository\u001b[0m\u001b[0m::new(server_state.db.clone());\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: consider importing this struct\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m1\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[38;5;10m+ use crate::db::repositories::role_repository::RoleRepository;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: if you import `RoleRepository`, refer to it directly\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m303\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m    let role_repo = \u001b[0m\u001b[0m\u001b[38;5;9mcrate::db::repositories::\u001b[0m\u001b[0mRoleRepository::new(server_state.db.clone());\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m303\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m    let role_repo = RoleRepository::new(server_state.db.clone());\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `sync_repository::*`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\db\\repositories\\mod.rs","byte_start":357,"byte_end":375,"line_start":14,"line_end":14,"column_start":9,"column_end":27,"is_primary":true,"text":[{"text":"pub use sync_repository::*;","highlight_start":9,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_imports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\db\\repositories\\mod.rs","byte_start":349,"byte_end":378,"line_start":14,"line_end":15,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"pub use sync_repository::*;","highlight_start":1,"highlight_end":28},{"text":"","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `sync_repository::*`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\db\\repositories\\mod.rs:14:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m14\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use sync_repository::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(unused_imports)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"field `sync_repo` of struct `SyncService` is private","code":{"code":"E0616","explanation":"Attempted to access a private field on a struct.\n\nErroneous code example:\n\n```compile_fail,E0616\nmod some_module {\n    pub struct Foo {\n        x: u32, // So `x` is private in here.\n    }\n\n    impl Foo {\n        pub fn new() -> Foo { Foo { x: 0 } }\n    }\n}\n\nlet f = some_module::Foo::new();\nprintln!(\"{}\", f.x); // error: field `x` of struct `some_module::Foo` is private\n```\n\nIf you want to access this field, you have two options:\n\n1) Set the field public:\n\n```\nmod some_module {\n    pub struct Foo {\n        pub x: u32, // `x` is now public.\n    }\n\n    impl Foo {\n        pub fn new() -> Foo { Foo { x: 0 } }\n    }\n}\n\nlet f = some_module::Foo::new();\nprintln!(\"{}\", f.x); // ok!\n```\n\n2) Add a getter function:\n\n```\nmod some_module {\n    pub struct Foo {\n        x: u32, // So `x` is still private in here.\n    }\n\n    impl Foo {\n        pub fn new() -> Foo { Foo { x: 0 } }\n\n        // We create the getter function here:\n        pub fn get_x(&self) -> &u32 { &self.x }\n    }\n}\n\nlet f = some_module::Foo::new();\nprintln!(\"{}\", f.get_x()); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\api\\handlers\\sync_handlers.rs","byte_start":7053,"byte_end":7062,"line_start":197,"line_end":197,"column_start":28,"column_end":37,"is_primary":true,"text":[{"text":"        match sync_service.sync_repo.get_role_memberships(&role_id).await {","highlight_start":28,"highlight_end":37}],"label":"private field","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0616]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: field `sync_repo` of struct `SyncService` is private\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\api\\handlers\\sync_handlers.rs:197:28\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m197\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        match sync_service.sync_repo.get_role_memberships(&role_id).await {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mprivate field\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no field `pool` on type `connection::Database`","code":{"code":"E0609","explanation":"Attempted to access a nonexistent field in a struct.\n\nErroneous code example:\n\n```compile_fail,E0609\nstruct StructWithFields {\n    x: u32,\n}\n\nlet s = StructWithFields { x: 0 };\nprintln!(\"{}\", s.foo); // error: no field `foo` on type `StructWithFields`\n```\n\nTo fix this error, check that you didn't misspell the field's name or that the\nfield actually exists. Example:\n\n```\nstruct StructWithFields {\n    x: u32,\n}\n\nlet s = StructWithFields { x: 0 };\nprintln!(\"{}\", s.x); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\db\\repositories\\sync_repository.rs","byte_start":2670,"byte_end":2674,"line_start":77,"line_end":77,"column_start":31,"column_end":35,"is_primary":true,"text":[{"text":"            .execute(&self.db.pool)","highlight_start":31,"highlight_end":35}],"label":"unknown field","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"available field is: `connection`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0609]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: no field `pool` on type `connection::Database`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\db\\repositories\\sync_repository.rs:77:31\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m77\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            .execute(&self.db.pool)\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                               \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9munknown field\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: available field is: `connection`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no field `pool` on type `connection::Database`","code":{"code":"E0609","explanation":"Attempted to access a nonexistent field in a struct.\n\nErroneous code example:\n\n```compile_fail,E0609\nstruct StructWithFields {\n    x: u32,\n}\n\nlet s = StructWithFields { x: 0 };\nprintln!(\"{}\", s.foo); // error: no field `foo` on type `StructWithFields`\n```\n\nTo fix this error, check that you didn't misspell the field's name or that the\nfield actually exists. Example:\n\n```\nstruct StructWithFields {\n    x: u32,\n}\n\nlet s = StructWithFields { x: 0 };\nprintln!(\"{}\", s.x); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\db\\repositories\\sync_repository.rs","byte_start":3603,"byte_end":3607,"line_start":106,"line_end":106,"column_start":33,"column_end":37,"is_primary":true,"text":[{"text":"            .fetch_all(&self.db.pool)","highlight_start":33,"highlight_end":37}],"label":"unknown field","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"available field is: `connection`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0609]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: no field `pool` on type `connection::Database`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\db\\repositories\\sync_repository.rs:106:33\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m106\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            .fetch_all(&self.db.pool)\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9munknown field\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: available field is: `connection`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no field `pool` on type `connection::Database`","code":{"code":"E0609","explanation":"Attempted to access a nonexistent field in a struct.\n\nErroneous code example:\n\n```compile_fail,E0609\nstruct StructWithFields {\n    x: u32,\n}\n\nlet s = StructWithFields { x: 0 };\nprintln!(\"{}\", s.foo); // error: no field `foo` on type `StructWithFields`\n```\n\nTo fix this error, check that you didn't misspell the field's name or that the\nfield actually exists. Example:\n\n```\nstruct StructWithFields {\n    x: u32,\n}\n\nlet s = StructWithFields { x: 0 };\nprintln!(\"{}\", s.x); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\db\\repositories\\sync_repository.rs","byte_start":4583,"byte_end":4587,"line_start":135,"line_end":135,"column_start":33,"column_end":37,"is_primary":true,"text":[{"text":"            .fetch_all(&self.db.pool)","highlight_start":33,"highlight_end":37}],"label":"unknown field","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"available field is: `connection`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0609]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: no field `pool` on type `connection::Database`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\db\\repositories\\sync_repository.rs:135:33\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m135\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            .fetch_all(&self.db.pool)\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9munknown field\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: available field is: `connection`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no field `pool` on type `connection::Database`","code":{"code":"E0609","explanation":"Attempted to access a nonexistent field in a struct.\n\nErroneous code example:\n\n```compile_fail,E0609\nstruct StructWithFields {\n    x: u32,\n}\n\nlet s = StructWithFields { x: 0 };\nprintln!(\"{}\", s.foo); // error: no field `foo` on type `StructWithFields`\n```\n\nTo fix this error, check that you didn't misspell the field's name or that the\nfield actually exists. Example:\n\n```\nstruct StructWithFields {\n    x: u32,\n}\n\nlet s = StructWithFields { x: 0 };\nprintln!(\"{}\", s.x); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\db\\repositories\\sync_repository.rs","byte_start":5416,"byte_end":5420,"line_start":163,"line_end":163,"column_start":31,"column_end":35,"is_primary":true,"text":[{"text":"            .execute(&self.db.pool)","highlight_start":31,"highlight_end":35}],"label":"unknown field","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"available field is: `connection`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0609]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: no field `pool` on type `connection::Database`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\db\\repositories\\sync_repository.rs:163:31\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m163\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            .execute(&self.db.pool)\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                               \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9munknown field\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: available field is: `connection`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no field `pool` on type `connection::Database`","code":{"code":"E0609","explanation":"Attempted to access a nonexistent field in a struct.\n\nErroneous code example:\n\n```compile_fail,E0609\nstruct StructWithFields {\n    x: u32,\n}\n\nlet s = StructWithFields { x: 0 };\nprintln!(\"{}\", s.foo); // error: no field `foo` on type `StructWithFields`\n```\n\nTo fix this error, check that you didn't misspell the field's name or that the\nfield actually exists. Example:\n\n```\nstruct StructWithFields {\n    x: u32,\n}\n\nlet s = StructWithFields { x: 0 };\nprintln!(\"{}\", s.x); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\db\\repositories\\sync_repository.rs","byte_start":6695,"byte_end":6699,"line_start":201,"line_end":201,"column_start":33,"column_end":37,"is_primary":true,"text":[{"text":"            .fetch_one(&self.db.pool)","highlight_start":33,"highlight_end":37}],"label":"unknown field","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"available field is: `connection`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0609]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: no field `pool` on type `connection::Database`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\db\\repositories\\sync_repository.rs:201:33\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m201\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            .fetch_one(&self.db.pool)\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9munknown field\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: available field is: `connection`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no field `pool` on type `connection::Database`","code":{"code":"E0609","explanation":"Attempted to access a nonexistent field in a struct.\n\nErroneous code example:\n\n```compile_fail,E0609\nstruct StructWithFields {\n    x: u32,\n}\n\nlet s = StructWithFields { x: 0 };\nprintln!(\"{}\", s.foo); // error: no field `foo` on type `StructWithFields`\n```\n\nTo fix this error, check that you didn't misspell the field's name or that the\nfield actually exists. Example:\n\n```\nstruct StructWithFields {\n    x: u32,\n}\n\nlet s = StructWithFields { x: 0 };\nprintln!(\"{}\", s.x); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\db\\repositories\\sync_repository.rs","byte_start":8494,"byte_end":8498,"line_start":256,"line_end":256,"column_start":31,"column_end":35,"is_primary":true,"text":[{"text":"            .execute(&self.db.pool)","highlight_start":31,"highlight_end":35}],"label":"unknown field","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"available field is: `connection`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0609]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: no field `pool` on type `connection::Database`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\db\\repositories\\sync_repository.rs:256:31\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m256\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            .execute(&self.db.pool)\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                               \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9munknown field\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: available field is: `connection`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no field `pool` on type `connection::Database`","code":{"code":"E0609","explanation":"Attempted to access a nonexistent field in a struct.\n\nErroneous code example:\n\n```compile_fail,E0609\nstruct StructWithFields {\n    x: u32,\n}\n\nlet s = StructWithFields { x: 0 };\nprintln!(\"{}\", s.foo); // error: no field `foo` on type `StructWithFields`\n```\n\nTo fix this error, check that you didn't misspell the field's name or that the\nfield actually exists. Example:\n\n```\nstruct StructWithFields {\n    x: u32,\n}\n\nlet s = StructWithFields { x: 0 };\nprintln!(\"{}\", s.x); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\db\\repositories\\sync_repository.rs","byte_start":9821,"byte_end":9825,"line_start":296,"line_end":296,"column_start":33,"column_end":37,"is_primary":true,"text":[{"text":"            .fetch_all(&self.db.pool)","highlight_start":33,"highlight_end":37}],"label":"unknown field","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"available field is: `connection`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0609]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: no field `pool` on type `connection::Database`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\db\\repositories\\sync_repository.rs:296:33\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m296\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            .fetch_all(&self.db.pool)\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9munknown field\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: available field is: `connection`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no field `pool` on type `connection::Database`","code":{"code":"E0609","explanation":"Attempted to access a nonexistent field in a struct.\n\nErroneous code example:\n\n```compile_fail,E0609\nstruct StructWithFields {\n    x: u32,\n}\n\nlet s = StructWithFields { x: 0 };\nprintln!(\"{}\", s.foo); // error: no field `foo` on type `StructWithFields`\n```\n\nTo fix this error, check that you didn't misspell the field's name or that the\nfield actually exists. Example:\n\n```\nstruct StructWithFields {\n    x: u32,\n}\n\nlet s = StructWithFields { x: 0 };\nprintln!(\"{}\", s.x); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\db\\repositories\\sync_repository.rs","byte_start":10699,"byte_end":10703,"line_start":326,"line_end":326,"column_start":31,"column_end":35,"is_primary":true,"text":[{"text":"            .execute(&self.db.pool)","highlight_start":31,"highlight_end":35}],"label":"unknown field","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"available field is: `connection`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0609]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: no field `pool` on type `connection::Database`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\db\\repositories\\sync_repository.rs:326:31\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m326\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            .execute(&self.db.pool)\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                               \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9munknown field\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: available field is: `connection`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no field `pool` on type `connection::Database`","code":{"code":"E0609","explanation":"Attempted to access a nonexistent field in a struct.\n\nErroneous code example:\n\n```compile_fail,E0609\nstruct StructWithFields {\n    x: u32,\n}\n\nlet s = StructWithFields { x: 0 };\nprintln!(\"{}\", s.foo); // error: no field `foo` on type `StructWithFields`\n```\n\nTo fix this error, check that you didn't misspell the field's name or that the\nfield actually exists. Example:\n\n```\nstruct StructWithFields {\n    x: u32,\n}\n\nlet s = StructWithFields { x: 0 };\nprintln!(\"{}\", s.x); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\db\\repositories\\sync_repository.rs","byte_start":11979,"byte_end":11983,"line_start":364,"line_end":364,"column_start":33,"column_end":37,"is_primary":true,"text":[{"text":"            .fetch_one(&self.db.pool)","highlight_start":33,"highlight_end":37}],"label":"unknown field","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"available field is: `connection`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0609]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: no field `pool` on type `connection::Database`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\db\\repositories\\sync_repository.rs:364:33\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m364\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            .fetch_one(&self.db.pool)\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9munknown field\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: available field is: `connection`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no field `pool` on type `connection::Database`","code":{"code":"E0609","explanation":"Attempted to access a nonexistent field in a struct.\n\nErroneous code example:\n\n```compile_fail,E0609\nstruct StructWithFields {\n    x: u32,\n}\n\nlet s = StructWithFields { x: 0 };\nprintln!(\"{}\", s.foo); // error: no field `foo` on type `StructWithFields`\n```\n\nTo fix this error, check that you didn't misspell the field's name or that the\nfield actually exists. Example:\n\n```\nstruct StructWithFields {\n    x: u32,\n}\n\nlet s = StructWithFields { x: 0 };\nprintln!(\"{}\", s.x); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\db\\repositories\\sync_repository.rs","byte_start":13753,"byte_end":13757,"line_start":419,"line_end":419,"column_start":31,"column_end":35,"is_primary":true,"text":[{"text":"            .execute(&self.db.pool)","highlight_start":31,"highlight_end":35}],"label":"unknown field","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"available field is: `connection`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0609]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: no field `pool` on type `connection::Database`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\db\\repositories\\sync_repository.rs:419:31\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m419\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            .execute(&self.db.pool)\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                               \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9munknown field\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: available field is: `connection`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no field `pool` on type `connection::Database`","code":{"code":"E0609","explanation":"Attempted to access a nonexistent field in a struct.\n\nErroneous code example:\n\n```compile_fail,E0609\nstruct StructWithFields {\n    x: u32,\n}\n\nlet s = StructWithFields { x: 0 };\nprintln!(\"{}\", s.foo); // error: no field `foo` on type `StructWithFields`\n```\n\nTo fix this error, check that you didn't misspell the field's name or that the\nfield actually exists. Example:\n\n```\nstruct StructWithFields {\n    x: u32,\n}\n\nlet s = StructWithFields { x: 0 };\nprintln!(\"{}\", s.x); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\db\\repositories\\sync_repository.rs","byte_start":15055,"byte_end":15059,"line_start":459,"line_end":459,"column_start":33,"column_end":37,"is_primary":true,"text":[{"text":"            .fetch_all(&self.db.pool)","highlight_start":33,"highlight_end":37}],"label":"unknown field","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"available field is: `connection`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0609]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: no field `pool` on type `connection::Database`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\db\\repositories\\sync_repository.rs:459:33\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m459\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            .fetch_all(&self.db.pool)\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9munknown field\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: available field is: `connection`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no field `pool` on type `connection::Database`","code":{"code":"E0609","explanation":"Attempted to access a nonexistent field in a struct.\n\nErroneous code example:\n\n```compile_fail,E0609\nstruct StructWithFields {\n    x: u32,\n}\n\nlet s = StructWithFields { x: 0 };\nprintln!(\"{}\", s.foo); // error: no field `foo` on type `StructWithFields`\n```\n\nTo fix this error, check that you didn't misspell the field's name or that the\nfield actually exists. Example:\n\n```\nstruct StructWithFields {\n    x: u32,\n}\n\nlet s = StructWithFields { x: 0 };\nprintln!(\"{}\", s.x); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\db\\repositories\\sync_repository.rs","byte_start":15910,"byte_end":15914,"line_start":489,"line_end":489,"column_start":31,"column_end":35,"is_primary":true,"text":[{"text":"            .execute(&self.db.pool)","highlight_start":31,"highlight_end":35}],"label":"unknown field","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"available field is: `connection`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0609]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: no field `pool` on type `connection::Database`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\db\\repositories\\sync_repository.rs:489:31\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m489\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            .execute(&self.db.pool)\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                               \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9munknown field\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: available field is: `connection`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no field `pool` on type `connection::Database`","code":{"code":"E0609","explanation":"Attempted to access a nonexistent field in a struct.\n\nErroneous code example:\n\n```compile_fail,E0609\nstruct StructWithFields {\n    x: u32,\n}\n\nlet s = StructWithFields { x: 0 };\nprintln!(\"{}\", s.foo); // error: no field `foo` on type `StructWithFields`\n```\n\nTo fix this error, check that you didn't misspell the field's name or that the\nfield actually exists. Example:\n\n```\nstruct StructWithFields {\n    x: u32,\n}\n\nlet s = StructWithFields { x: 0 };\nprintln!(\"{}\", s.x); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\db\\repositories\\sync_repository.rs","byte_start":16578,"byte_end":16582,"line_start":514,"line_end":514,"column_start":31,"column_end":35,"is_primary":true,"text":[{"text":"            .execute(&self.db.pool)","highlight_start":31,"highlight_end":35}],"label":"unknown field","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"available field is: `connection`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0609]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: no field `pool` on type `connection::Database`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\db\\repositories\\sync_repository.rs:514:31\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m514\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            .execute(&self.db.pool)\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                               \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9munknown field\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: available field is: `connection`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no field `pool` on type `connection::Database`","code":{"code":"E0609","explanation":"Attempted to access a nonexistent field in a struct.\n\nErroneous code example:\n\n```compile_fail,E0609\nstruct StructWithFields {\n    x: u32,\n}\n\nlet s = StructWithFields { x: 0 };\nprintln!(\"{}\", s.foo); // error: no field `foo` on type `StructWithFields`\n```\n\nTo fix this error, check that you didn't misspell the field's name or that the\nfield actually exists. Example:\n\n```\nstruct StructWithFields {\n    x: u32,\n}\n\nlet s = StructWithFields { x: 0 };\nprintln!(\"{}\", s.x); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\db\\repositories\\sync_repository.rs","byte_start":16800,"byte_end":16804,"line_start":521,"line_end":521,"column_start":31,"column_end":35,"is_primary":true,"text":[{"text":"            .execute(&self.db.pool)","highlight_start":31,"highlight_end":35}],"label":"unknown field","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"available field is: `connection`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0609]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: no field `pool` on type `connection::Database`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\db\\repositories\\sync_repository.rs:521:31\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m521\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            .execute(&self.db.pool)\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                               \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9munknown field\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: available field is: `connection`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no field `pool` on type `connection::Database`","code":{"code":"E0609","explanation":"Attempted to access a nonexistent field in a struct.\n\nErroneous code example:\n\n```compile_fail,E0609\nstruct StructWithFields {\n    x: u32,\n}\n\nlet s = StructWithFields { x: 0 };\nprintln!(\"{}\", s.foo); // error: no field `foo` on type `StructWithFields`\n```\n\nTo fix this error, check that you didn't misspell the field's name or that the\nfield actually exists. Example:\n\n```\nstruct StructWithFields {\n    x: u32,\n}\n\nlet s = StructWithFields { x: 0 };\nprintln!(\"{}\", s.x); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\db\\repositories\\sync_repository.rs","byte_start":17638,"byte_end":17642,"line_start":551,"line_end":551,"column_start":33,"column_end":37,"is_primary":true,"text":[{"text":"            .fetch_one(&self.db.pool)","highlight_start":33,"highlight_end":37}],"label":"unknown field","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"available field is: `connection`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0609]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: no field `pool` on type `connection::Database`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\db\\repositories\\sync_repository.rs:551:33\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m551\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            .fetch_one(&self.db.pool)\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9munknown field\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: available field is: `connection`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no field `pool` on type `connection::Database`","code":{"code":"E0609","explanation":"Attempted to access a nonexistent field in a struct.\n\nErroneous code example:\n\n```compile_fail,E0609\nstruct StructWithFields {\n    x: u32,\n}\n\nlet s = StructWithFields { x: 0 };\nprintln!(\"{}\", s.foo); // error: no field `foo` on type `StructWithFields`\n```\n\nTo fix this error, check that you didn't misspell the field's name or that the\nfield actually exists. Example:\n\n```\nstruct StructWithFields {\n    x: u32,\n}\n\nlet s = StructWithFields { x: 0 };\nprintln!(\"{}\", s.x); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\db\\repositories\\sync_repository.rs","byte_start":17835,"byte_end":17839,"line_start":557,"line_end":557,"column_start":33,"column_end":37,"is_primary":true,"text":[{"text":"            .fetch_one(&self.db.pool)","highlight_start":33,"highlight_end":37}],"label":"unknown field","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"available field is: `connection`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0609]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: no field `pool` on type `connection::Database`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\db\\repositories\\sync_repository.rs:557:33\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m557\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            .fetch_one(&self.db.pool)\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9munknown field\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: available field is: `connection`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no field `pool` on type `connection::Database`","code":{"code":"E0609","explanation":"Attempted to access a nonexistent field in a struct.\n\nErroneous code example:\n\n```compile_fail,E0609\nstruct StructWithFields {\n    x: u32,\n}\n\nlet s = StructWithFields { x: 0 };\nprintln!(\"{}\", s.foo); // error: no field `foo` on type `StructWithFields`\n```\n\nTo fix this error, check that you didn't misspell the field's name or that the\nfield actually exists. Example:\n\n```\nstruct StructWithFields {\n    x: u32,\n}\n\nlet s = StructWithFields { x: 0 };\nprintln!(\"{}\", s.x); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\db\\repositories\\sync_repository.rs","byte_start":18034,"byte_end":18038,"line_start":563,"line_end":563,"column_start":33,"column_end":37,"is_primary":true,"text":[{"text":"            .fetch_one(&self.db.pool)","highlight_start":33,"highlight_end":37}],"label":"unknown field","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"available field is: `connection`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0609]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: no field `pool` on type `connection::Database`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\db\\repositories\\sync_repository.rs:563:33\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m563\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            .fetch_one(&self.db.pool)\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9munknown field\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: available field is: `connection`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `owner_role_id`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\db\\seed.rs","byte_start":5782,"byte_end":5795,"line_start":158,"line_end":158,"column_start":5,"column_end":18,"is_primary":true,"text":[{"text":"    owner_role_id: &Uuid,","highlight_start":5,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_variables)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\db\\seed.rs","byte_start":5782,"byte_end":5795,"line_start":158,"line_end":158,"column_start":5,"column_end":18,"is_primary":true,"text":[{"text":"    owner_role_id: &Uuid,","highlight_start":5,"highlight_end":18}],"label":null,"suggested_replacement":"_owner_role_id","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `owner_role_id`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\db\\seed.rs:158:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m158\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    owner_role_id: &Uuid,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_owner_role_id`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(unused_variables)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `member_role_id`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\db\\seed.rs","byte_start":5809,"byte_end":5823,"line_start":159,"line_end":159,"column_start":5,"column_end":19,"is_primary":true,"text":[{"text":"    member_role_id: &Uuid,","highlight_start":5,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\db\\seed.rs","byte_start":5809,"byte_end":5823,"line_start":159,"line_end":159,"column_start":5,"column_end":19,"is_primary":true,"text":[{"text":"    member_role_id: &Uuid,","highlight_start":5,"highlight_end":19}],"label":null,"suggested_replacement":"_member_role_id","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `member_role_id`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\db\\seed.rs:159:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m159\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    member_role_id: &Uuid,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_member_role_id`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"expected `{closure@sync_service.rs:396:70}` to return `&&str`, but it returns `&String`","code":{"code":"E0271","explanation":"A type mismatched an associated type of a trait.\n\nErroneous code example:\n\n```compile_fail,E0271\ntrait Trait { type AssociatedType; }\n\nfn foo<T>(t: T) where T: Trait<AssociatedType=u32> {\n//                    ~~~~~~~~ ~~~~~~~~~~~~~~~~~~\n//                        |            |\n//         This says `foo` can         |\n//           only be used with         |\n//              some type that         |\n//         implements `Trait`.         |\n//                                     |\n//                             This says not only must\n//                             `T` be an impl of `Trait`\n//                             but also that the impl\n//                             must assign the type `u32`\n//                             to the associated type.\n    println!(\"in foo\");\n}\n\nimpl Trait for i8 { type AssociatedType = &'static str; }\n//~~~~~~~~~~~~~~~   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\n//      |                             |\n// `i8` does have                     |\n// implementation                     |\n// of `Trait`...                      |\n//                     ... but it is an implementation\n//                     that assigns `&'static str` to\n//                     the associated type.\n\nfoo(3_i8);\n// Here, we invoke `foo` with an `i8`, which does not satisfy\n// the constraint `<i8 as Trait>::AssociatedType=u32`, and\n// therefore the type-checker complains with this error code.\n```\n\nThe issue can be resolved by changing the associated type:\n1) in the `foo` implementation:\n```\ntrait Trait { type AssociatedType; }\n\nfn foo<T>(t: T) where T: Trait<AssociatedType = &'static str> {\n    println!(\"in foo\");\n}\n\nimpl Trait for i8 { type AssociatedType = &'static str; }\n\nfoo(3_i8);\n```\n\n2) in the `Trait` implementation for `i8`:\n```\ntrait Trait { type AssociatedType; }\n\nfn foo<T>(t: T) where T: Trait<AssociatedType = u32> {\n    println!(\"in foo\");\n}\n\nimpl Trait for i8 { type AssociatedType = u32; }\n\nfoo(3_i8);\n```\n"},"level":"error","spans":[{"file_name":"src\\services\\sync_service.rs","byte_start":14176,"byte_end":14179,"line_start":396,"line_end":396,"column_start":70,"column_end":73,"is_primary":false,"text":[{"text":"        for role_name in default_roles.iter().chain(roles.iter().map(|r| &r.name)) {","highlight_start":70,"highlight_end":73}],"label":"this closure","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\services\\sync_service.rs","byte_start":14180,"byte_end":14187,"line_start":396,"line_end":396,"column_start":74,"column_end":81,"is_primary":true,"text":[{"text":"        for role_name in default_roles.iter().chain(roles.iter().map(|r| &r.name)) {","highlight_start":74,"highlight_end":81}],"label":"expected `&&str`, found `&String`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\services\\sync_service.rs","byte_start":14153,"byte_end":14158,"line_start":396,"line_end":396,"column_start":47,"column_end":52,"is_primary":false,"text":[{"text":"        for role_name in default_roles.iter().chain(roles.iter().map(|r| &r.name)) {","highlight_start":47,"highlight_end":52}],"label":"required by a bound introduced by this call","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"expected reference `&&str`\n   found reference `&std::string::String`","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"required for `Map<Iter<'_, Role>, {closure@sync_service.rs:396:70}>` to implement `Iterator`","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"required by a bound in `std::iter::Iterator::chain`","code":null,"level":"note","spans":[{"file_name":"C:\\Users\\<USER>\\.rustup\\toolchains\\stable-x86_64-pc-windows-msvc\\lib/rustlib/src/rust\\library\\core\\src\\iter\\traits\\iterator.rs","byte_start":15962,"byte_end":15967,"line_start":468,"line_end":468,"column_start":8,"column_end":13,"is_primary":false,"text":[{"text":"    fn chain<U>(self, other: U) -> Chain<Self, U::IntoIter>","highlight_start":8,"highlight_end":13}],"label":"required by a bound in this associated function","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"C:\\Users\\<USER>\\.rustup\\toolchains\\stable-x86_64-pc-windows-msvc\\lib/rustlib/src/rust\\library\\core\\src\\iter\\traits\\iterator.rs","byte_start":16070,"byte_end":16087,"line_start":471,"line_end":471,"column_start":25,"column_end":42,"is_primary":true,"text":[{"text":"        U: IntoIterator<Item = Self::Item>,","highlight_start":25,"highlight_end":42}],"label":"required by this bound in `Iterator::chain`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"the full name for the type has been written to 'K:\\Prog\\enki-mr\\ki\\target\\debug\\deps\\ki-9e1264a00ca98064.long-type-17895379978149344849.txt'","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"consider using `--verbose` to print the full type name to the console","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0271]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: expected `{closure@sync_service.rs:396:70}` to return `&&str`, but it returns `&String`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\services\\sync_service.rs:396:74\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m396\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        for role_name in default_roles.iter().chain(roles.iter().map(|r| &r.name)) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                               \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-----\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m---\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mexpected `&&str`, found `&String`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                               \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                               \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mthis closure\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                               \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mrequired by a bound introduced by this call\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: expected reference `&\u001b[0m\u001b[0m\u001b[1m\u001b[35m&str\u001b[0m\u001b[0m`\u001b[0m\n\u001b[0m               found reference `&\u001b[0m\u001b[0m\u001b[1m\u001b[35mstd::string::String\u001b[0m\u001b[0m`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: required for `Map<Iter<'_, Role>, {closure@sync_service.rs:396:70}>` to implement `Iterator`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: required by a bound in `std::iter::Iterator::chain`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\.rustup\\toolchains\\stable-x86_64-pc-windows-msvc\\lib/rustlib/src/rust\\library\\core\\src\\iter\\traits\\iterator.rs:471:25\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m468\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn chain<U>(self, other: U) -> Chain<Self, U::IntoIter>\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-----\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mrequired by a bound in this associated function\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m471\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        U: IntoIterator<Item = Self::Item>,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10mrequired by this bound in `Iterator::chain`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: the full name for the type has been written to 'K:\\Prog\\enki-mr\\ki\\target\\debug\\deps\\ki-9e1264a00ca98064.long-type-17895379978149344849.txt'\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: consider using `--verbose` to print the full type name to the console\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"expected `{closure@sync_service.rs:396:70}` to return `&&str`, but it returns `&String`","code":{"code":"E0271","explanation":"A type mismatched an associated type of a trait.\n\nErroneous code example:\n\n```compile_fail,E0271\ntrait Trait { type AssociatedType; }\n\nfn foo<T>(t: T) where T: Trait<AssociatedType=u32> {\n//                    ~~~~~~~~ ~~~~~~~~~~~~~~~~~~\n//                        |            |\n//         This says `foo` can         |\n//           only be used with         |\n//              some type that         |\n//         implements `Trait`.         |\n//                                     |\n//                             This says not only must\n//                             `T` be an impl of `Trait`\n//                             but also that the impl\n//                             must assign the type `u32`\n//                             to the associated type.\n    println!(\"in foo\");\n}\n\nimpl Trait for i8 { type AssociatedType = &'static str; }\n//~~~~~~~~~~~~~~~   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\n//      |                             |\n// `i8` does have                     |\n// implementation                     |\n// of `Trait`...                      |\n//                     ... but it is an implementation\n//                     that assigns `&'static str` to\n//                     the associated type.\n\nfoo(3_i8);\n// Here, we invoke `foo` with an `i8`, which does not satisfy\n// the constraint `<i8 as Trait>::AssociatedType=u32`, and\n// therefore the type-checker complains with this error code.\n```\n\nThe issue can be resolved by changing the associated type:\n1) in the `foo` implementation:\n```\ntrait Trait { type AssociatedType; }\n\nfn foo<T>(t: T) where T: Trait<AssociatedType = &'static str> {\n    println!(\"in foo\");\n}\n\nimpl Trait for i8 { type AssociatedType = &'static str; }\n\nfoo(3_i8);\n```\n\n2) in the `Trait` implementation for `i8`:\n```\ntrait Trait { type AssociatedType; }\n\nfn foo<T>(t: T) where T: Trait<AssociatedType = u32> {\n    println!(\"in foo\");\n}\n\nimpl Trait for i8 { type AssociatedType = u32; }\n\nfoo(3_i8);\n```\n"},"level":"error","spans":[{"file_name":"src\\services\\sync_service.rs","byte_start":14176,"byte_end":14179,"line_start":396,"line_end":396,"column_start":70,"column_end":73,"is_primary":false,"text":[{"text":"        for role_name in default_roles.iter().chain(roles.iter().map(|r| &r.name)) {","highlight_start":70,"highlight_end":73}],"label":"this closure","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\services\\sync_service.rs","byte_start":14180,"byte_end":14187,"line_start":396,"line_end":396,"column_start":74,"column_end":81,"is_primary":true,"text":[{"text":"        for role_name in default_roles.iter().chain(roles.iter().map(|r| &r.name)) {","highlight_start":74,"highlight_end":81}],"label":"expected `&&str`, found `&String`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"expected reference `&&str`\n   found reference `&std::string::String`","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"required for `Map<Iter<'_, Role>, {closure@sync_service.rs:396:70}>` to implement `Iterator`","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"1 redundant requirement hidden","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"required for `Chain<Iter<'_, &str>, Map<Iter<'_, Role>, {closure@...}>>` to implement `Iterator`","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"required for `Chain<Iter<'_, &str>, Map<Iter<'_, Role>, {closure@...}>>` to implement `IntoIterator`","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"the full name for the type has been written to 'K:\\Prog\\enki-mr\\ki\\target\\debug\\deps\\ki-9e1264a00ca98064.long-type-10066290798091832116.txt'","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"consider using `--verbose` to print the full type name to the console","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0271]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: expected `{closure@sync_service.rs:396:70}` to return `&&str`, but it returns `&String`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\services\\sync_service.rs:396:74\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m396\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        for role_name in default_roles.iter().chain(roles.iter().map(|r| &r.name)) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                                      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m---\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mexpected `&&str`, found `&String`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                                      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                                      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mthis closure\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: expected reference `&\u001b[0m\u001b[0m\u001b[1m\u001b[35m&str\u001b[0m\u001b[0m`\u001b[0m\n\u001b[0m               found reference `&\u001b[0m\u001b[0m\u001b[1m\u001b[35mstd::string::String\u001b[0m\u001b[0m`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: required for `Map<Iter<'_, Role>, {closure@sync_service.rs:396:70}>` to implement `Iterator`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: 1 redundant requirement hidden\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: required for `Chain<Iter<'_, &str>, Map<Iter<'_, Role>, {closure@...}>>` to implement `Iterator`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: required for `Chain<Iter<'_, &str>, Map<Iter<'_, Role>, {closure@...}>>` to implement `IntoIterator`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: the full name for the type has been written to 'K:\\Prog\\enki-mr\\ki\\target\\debug\\deps\\ki-9e1264a00ca98064.long-type-10066290798091832116.txt'\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: consider using `--verbose` to print the full type name to the console\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"`if` and `else` have incompatible types","code":{"code":"E0308","explanation":"Expected type did not match the received type.\n\nErroneous code examples:\n\n```compile_fail,E0308\nfn plus_one(x: i32) -> i32 {\n    x + 1\n}\n\nplus_one(\"Not a number\");\n//       ^^^^^^^^^^^^^^ expected `i32`, found `&str`\n\nif \"Not a bool\" {\n// ^^^^^^^^^^^^ expected `bool`, found `&str`\n}\n\nlet x: f32 = \"Not a float\";\n//     ---   ^^^^^^^^^^^^^ expected `f32`, found `&str`\n//     |\n//     expected due to this\n```\n\nThis error occurs when an expression was used in a place where the compiler\nexpected an expression of a different type. It can occur in several cases, the\nmost common being when calling a function and passing an argument which has a\ndifferent type than the matching type in the function declaration.\n"},"level":"error","spans":[{"file_name":"src\\services\\sync_service.rs","byte_start":14357,"byte_end":14374,"line_start":400,"line_end":400,"column_start":17,"column_end":34,"is_primary":true,"text":[{"text":"                role_name.clone()","highlight_start":17,"highlight_end":34}],"label":"expected `String`, found `&str`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\services\\sync_service.rs","byte_start":14295,"byte_end":14319,"line_start":398,"line_end":398,"column_start":17,"column_end":41,"is_primary":false,"text":[{"text":"                role_name.to_lowercase()","highlight_start":17,"highlight_end":41}],"label":"expected because of this","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\services\\sync_service.rs","byte_start":14226,"byte_end":14388,"line_start":397,"line_end":401,"column_start":35,"column_end":14,"is_primary":false,"text":[{"text":"            let role_identifier = if role_name == &\"Owner\" || role_name == &\"Member\" {","highlight_start":35,"highlight_end":87},{"text":"                role_name.to_lowercase()","highlight_start":1,"highlight_end":41},{"text":"            } else {","highlight_start":1,"highlight_end":21},{"text":"                role_name.clone()","highlight_start":1,"highlight_end":34},{"text":"            };","highlight_start":1,"highlight_end":14}],"label":"`if` and `else` have incompatible types","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"try using a conversion method","code":null,"level":"help","spans":[{"file_name":"src\\services\\sync_service.rs","byte_start":14367,"byte_end":14372,"line_start":400,"line_end":400,"column_start":27,"column_end":32,"is_primary":true,"text":[{"text":"                role_name.clone()","highlight_start":27,"highlight_end":32}],"label":null,"suggested_replacement":"to_string","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0308]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: `if` and `else` have incompatible types\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\services\\sync_service.rs:400:17\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m397\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m            let role_identifier = if role_name == &\"Owner\" || role_name == &\"Member\" {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m ___________________________________-\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m398\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                role_name.to_lowercase()\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mexpected because of this\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m399\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            } else {\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m400\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                role_name.clone()\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-----\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mhelp: try using a conversion method: `to_string`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mexpected `String`, found `&str`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m401\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            };\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|_____________-\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m`if` and `else` have incompatible types\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"aborting due to 39 previous errors; 3 warnings emitted","code":null,"level":"error","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: aborting due to 39 previous errors; 3 warnings emitted\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"Some errors have detailed explanations: E0271, E0308, E0433, E0609, E0616.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;15mSome errors have detailed explanations: E0271, E0308, E0433, E0609, E0616.\u001b[0m\n"}
{"$message_type":"diagnostic","message":"For more information about an error, try `rustc --explain E0271`.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;15mFor more information about an error, try `rustc --explain E0271`.\u001b[0m\n"}
