use axum::{
    Router,
    routing::{get, post, put, delete},
    middleware,
};
use crate::{
    api::{
        auth::session_auth,
        middleware::zed_token::zed_token_middleware,
        handlers::{
            firebase_handlers::connect,
            task_handlers::{
                create_task, delete_task, get_task, get_tasks, move_task, update_task
            },
            user_handlers::{
                create_user, create_manual_user, delete_user, get_user, get_user_by_firebase_id, get_users, update_user, upsert_user
            },
            agent_handlers::{
                create_agent, delete_agent, get_agent, get_agents, update_agent
            },
            permission_handlers::{
                create_permission, delete_permission, get_permissions, update_permission
            },
            role_handlers::{
                create_role, delete_role, get_role, get_roles, update_role,
                get_role_tree, get_role_details, set_role_parent,
                bulk_relationships, check_permissions,
                get_role_permissions, edit_role_permissions
            },
            sync_handlers::{
                get_sync_status, trigger_sync, spicedb_health,
                get_role_members_from_sync, get_user_permissions_summary,
                get_agent_permissions_summary
            },
        },
    },
    ServerState
};

/// Create API router
pub fn create_router(server_state: ServerState) -> Router {
    // Public routes (no authentication required)
    let public_routes = Router::new()
        .route("/connect", post(connect))
        // Temporary public role endpoint for testing
        .route("/roles/public", get(get_roles));

    // Protected routes (session authentication required)
    let protected_routes = Router::new()
        // Task routes
        .route("/tasks", post(create_task))
        .route("/tasks", get(get_tasks))
        .route("/tasks/{id}", get(get_task))
        .route("/tasks/{id}", put(update_task))
        .route("/tasks/{id}", delete(delete_task))
        .route("/tasks/{id}/move", post(move_task))

        // User routes
        .route("/users", post(create_user))
        .route("/users", get(get_users))
        .route("/users/{id}", get(get_user))
        .route("/users/{id}", put(update_user))
        .route("/users/{id}", delete(delete_user))
        .route("/users/upsert", post(upsert_user))
        .route("/users/manual", post(create_manual_user))
        .route("/users/firebase/{firebase_user_id}", get(get_user_by_firebase_id))

        // Agent routes
        .route("/agents", post(create_agent))
        .route("/agents", get(get_agents))
        .route("/agents/{id}", get(get_agent))
        .route("/agents/{id}", put(update_agent))
        .route("/agents/{id}", delete(delete_agent))

        // Permission routes
        .route("/permissions", post(create_permission))
        .route("/permissions", get(get_permissions))
        .route("/permissions/{id}", put(update_permission))
        .route("/permissions/{id}", delete(delete_permission))

        // Role routes
        .route("/roles", post(create_role))
        .route("/roles", get(get_roles))
        .route("/roles/{id}", get(get_role))
        .route("/roles/{id}", put(update_role))
        .route("/roles/{id}", delete(delete_role))
        .route("/roles/tree", get(get_role_tree))
        .route("/roles/{id}/details", get(get_role_details))
        .route("/roles/{id}/permissions", get(get_role_permissions))
        .route("/roles/{id}/permissions", put(edit_role_permissions))
        .route("/roles/{id}/parent", put(set_role_parent))
        .route("/transactions/relationships", post(bulk_relationships))
        .route("/permissions/check", post(check_permissions))

        // Sync routes
        .route("/sync/status", get(get_sync_status))
        .route("/sync/trigger", post(trigger_sync))
        .route("/sync/health", get(spicedb_health))
        .route("/sync/roles/{id}/members", get(get_role_members_from_sync))
        .route("/sync/users/{id}/permissions", get(get_user_permissions_summary))
        .route("/sync/agents/{id}/permissions", get(get_agent_permissions_summary))

        // Apply session authentication middleware to all protected routes
        .route_layer(middleware::from_fn_with_state(server_state.clone(), session_auth))
        // Apply ZedToken middleware for SpiceDB consistency
        .route_layer(middleware::from_fn(zed_token_middleware));

    // Combine public and protected routes
    Router::new()
        .merge(public_routes)
        .merge(protected_routes)
        .with_state(server_state)
}
