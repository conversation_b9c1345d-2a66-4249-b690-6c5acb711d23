use anyhow::Result;
use tracing::{info, warn, error};
use uuid::Uuid;

use crate::{
    db::{
        Database,
        repositories::{
            user_repository::UserRepository,
            agent_repository::AgentRepository,
            role_repository::RoleRepository,
            // sync_repository::SyncRepository,
        },
        // models::{
        //     NewRoleMembership, NewUserPermission, NewAgentPermission,
        //     PermissionSummary, RoleInfo, PermissionInfo,
        // },
    },
    services::spicedb_service::SpiceDBService,
};

/// Service for synchronizing SQLite database with SpiceDB relationships
pub struct SyncService {
    db: Database,
    spicedb: SpiceDBService,
    // sync_repo: SyncRepository,
}

impl SyncService {
    pub fn new(db: Database, spicedb: SpiceDBService) -> Self {
        // let sync_repo = SyncRepository::new(db.clone());
        Self { db, spicedb }
    }

    /// Sync all data from SQLite to SpiceDB
    pub async fn sync_all(&self) -> Result<()> {
        info!("Starting full synchronization between SQLite and SpiceDB");

        // Sync users first (they're referenced by other entities)
        self.sync_users().await?;
        
        // Sync agents
        self.sync_agents().await?;
        
        // Sync roles and their relationships
        self.sync_roles().await?;

        // Set up default permissions
        self.setup_default_permissions().await?;

        // Sync role memberships to SQLite
        // self.sync_role_memberships_to_sqlite().await?;

        // Sync permissions to SQLite
        // self.sync_permissions_to_sqlite().await?;

        info!("Full synchronization completed successfully");
        Ok(())
    }

    /// Sync all users from SQLite to SpiceDB
    async fn sync_users(&self) -> Result<()> {
        info!("Syncing users to SpiceDB");

        let user_repo = UserRepository::new(self.db.clone());
        let users = user_repo.get_all().await?;

        // Check if we have any existing owner role assignments
        let has_existing_owner = self.check_existing_owner().await?;

        for (index, user) in users.iter().enumerate() {
            // Grant basic view permission to themselves using correct relation name
            if let Err(e) = self.spicedb.grant_resource_permission(
                "user",
                &user.id.to_string(),
                "view", // Use 'view' permission which maps to viewer_user relation
                &user.user_id, // Firebase user ID
            ).await {
                warn!("Failed to grant self-view permission for user {}: {}", user.id, e);
            }

            // Assign appropriate role based on user position and existing owners
            if index == 0 && !has_existing_owner {
                // First user gets Owner role only if no owner exists
                if let Err(e) = self.assign_owner_role(&user.user_id).await {
                    warn!("Failed to assign Owner role to first user {}: {}", user.email, e);
                } else {
                    info!("Assigned Owner role to first user: {}", user.email);
                }
            } else {
                // All other users get Member role
                if let Err(e) = self.assign_member_role(&user.user_id).await {
                    warn!("Failed to assign Member role to user {}: {}", user.email, e);
                } else {
                    info!("Assigned Member role to user: {}", user.email);
                }
            }

            info!("Synced user: {} ({})", user.email, user.id);
        }

        Ok(())
    }

    /// Check if there's already an owner in the system
    async fn check_existing_owner(&self) -> Result<bool> {
        // Check if anyone is already assigned to the owner role
        match self.spicedb.lookup_subjects("role", "owner", "member").await {
            Ok(subjects) => {
                let has_owner = !subjects.is_empty();
                info!("Existing owner check: {}", if has_owner { "found existing owner" } else { "no existing owner" });
                Ok(has_owner)
            }
            Err(e) => {
                warn!("Failed to check existing owner, assuming none exists: {}", e);
                Ok(false)
            }
        }
    }

    /// Sync all agents from SQLite to SpiceDB
    async fn sync_agents(&self) -> Result<()> {
        info!("Syncing agents to SpiceDB");
        
        let agent_repo = AgentRepository::new(self.db.clone());
        let agents = agent_repo.get_all().await?;

        for agent in agents {
            // Set creator relationship for the agent
            if let Err(e) = self.spicedb.set_creator(
                "agent",
                &agent.id.to_string(),
                &agent.created_by,
            ).await {
                warn!("Failed to set creator for agent {}: {}", agent.id, e);
            }

            info!("Synced agent: {} ({})", agent.name, agent.id);
        }

        Ok(())
    }

    /// Sync all roles from SQLite to SpiceDB
    async fn sync_roles(&self) -> Result<()> {
        info!("Syncing roles to SpiceDB");
        
        let role_repo = RoleRepository::new(self.db.clone());
        let roles = role_repo.get_all().await?;

        for role in roles {
            info!("Synced role: {} ({})", role.name, role.id);
        }

        Ok(())
    }

    /// Set up default permissions and roles for system administration
    async fn setup_default_permissions(&self) -> Result<()> {
        info!("Setting up default permissions and roles");

        // Create default roles with proper permissions
        self.create_default_roles().await?;

        // Find the first user to make them an owner (for initial setup)
        let user_repo = UserRepository::new(self.db.clone());
        let users = user_repo.get_all().await?;

        if let Some(first_user) = users.first() {
            // Assign Owner role to the first user
            if let Err(e) = self.assign_owner_role(&first_user.user_id).await {
                warn!("Failed to assign owner role to first user: {}", e);
            } else {
                info!("Assigned owner role to user: {}", first_user.email);
            }
        }

        Ok(())
    }

    /// Create default roles with comprehensive CRUD permissions
    async fn create_default_roles(&self) -> Result<()> {
        info!("Creating default roles with permissions");

        // Create Owner role with full permissions
        self.create_owner_role().await?;

        // Create Member role with basic permissions
        self.create_member_role().await?;

        Ok(())
    }

    /// Create Owner role with full admin permissions
    async fn create_owner_role(&self) -> Result<()> {
        let owner_role_id = "owner";

        // Grant comprehensive permissions to Owner role using role relationships
        let owner_relationships = vec![
            // Global settings permissions for Owner role
            ("global_settings", "system", "admin_role"),
            ("global_settings", "system", "owner_role"),
        ];

        for (resource_type, resource_id, relation) in owner_relationships {
            if let Err(e) = self.spicedb.add_role_relationship(
                resource_type,
                resource_id,
                relation,
                owner_role_id,
            ).await {
                warn!("Failed to grant {} relation to Owner role: {}", relation, e);
            }
        }

        info!("Created Owner role with full permissions");
        Ok(())
    }

    /// Create Member role with basic permissions
    async fn create_member_role(&self) -> Result<()> {
        // Members get minimal permissions - they can only create and manage their own tasks
        // No global permissions are granted to Members by default
        // Individual task permissions will be granted when they create tasks

        info!("Created Member role with basic permissions (no global permissions)");
        Ok(())
    }

    /// Assign Owner role to a user
    pub async fn assign_owner_role(&self, user_firebase_id: &str) -> Result<()> {
        self.spicedb.add_role_member("owner", user_firebase_id).await?;
        info!("Assigned Owner role to user: {}", user_firebase_id);

        // Also ensure the Owner role exists in SQLite for UI display
        self.ensure_default_roles_exist().await?;

        Ok(())
    }

    /// Assign Member role to a user (default for new users)
    pub async fn assign_member_role(&self, user_firebase_id: &str) -> Result<()> {
        self.spicedb.add_role_member("member", user_firebase_id).await?;
        info!("Assigned Member role to user: {}", user_firebase_id);

        // Also ensure the Member role exists in SQLite for UI display
        self.ensure_default_roles_exist().await?;

        Ok(())
    }

    /// Ensure default roles exist in SQLite (for UI display)
    async fn ensure_default_roles_exist(&self) -> Result<()> {
        let role_repo = RoleRepository::new(self.db.clone());

        // Check if Owner role exists
        if role_repo.get_by_name("Owner").await?.is_none() {
            let owner_role = crate::db::models::NewRole {
                name: "Owner".to_string(),
                description: Some("Full system ownership with all administrative privileges".to_string()),
                color: Some("#dc2626".to_string()),
            };
            if let Err(e) = role_repo.create(owner_role).await {
                warn!("Failed to create Owner role in SQLite: {}", e);
            }
        }

        // Check if Member role exists
        if role_repo.get_by_name("Member").await?.is_none() {
            let member_role = crate::db::models::NewRole {
                name: "Member".to_string(),
                description: Some("Standard member access with basic permissions".to_string()),
                color: Some("#2563eb".to_string()),
            };
            if let Err(e) = role_repo.create(member_role).await {
                warn!("Failed to create Member role in SQLite: {}", e);
            }
        }

        Ok(())
    }

    /// Sync a specific user when they're created/updated
    pub async fn sync_user(&self, user_id: Uuid) -> Result<()> {
        let user_repo = UserRepository::new(self.db.clone());

        if let Some(user) = user_repo.get_by_id(user_id).await? {
            // Grant self-view permission
            self.spicedb.grant_resource_permission(
                "user",
                &user.id.to_string(),
                "view",
                &user.user_id,
            ).await?;

            // Assign Member role to new users (Owner role is only assigned during initial setup)
            if let Err(e) = self.assign_member_role(&user.user_id).await {
                warn!("Failed to assign Member role to new user {}: {}", user.email, e);
            }

            info!("Synced user: {} ({}) with Member role", user.email, user.id);
        }

        Ok(())
    }

    /// Sync a specific agent when it's created/updated
    pub async fn sync_agent(&self, agent_id: Uuid, creator_firebase_id: &str) -> Result<()> {
        // Set creator relationship
        self.spicedb.set_creator(
            "agent",
            &agent_id.to_string(),
            creator_firebase_id,
        ).await?;

        info!("Synced agent: {}", agent_id);
        Ok(())
    }

    /// Grant role membership to a user
    pub async fn add_user_to_role(&self, role_id: &str, user_firebase_id: &str) -> Result<()> {
        self.spicedb.add_role_member(role_id, user_firebase_id).await?;
        info!("Added user {} to role {}", user_firebase_id, role_id);
        Ok(())
    }

    /// Remove role membership from a user
    pub async fn remove_user_from_role(&self, role_id: &str, user_firebase_id: &str) -> Result<()> {
        self.spicedb.remove_role_member(role_id, user_firebase_id).await?;
        info!("Removed user {} from role {}", user_firebase_id, role_id);
        Ok(())
    }

    /// Grant task permissions when a task is created
    pub async fn sync_task(&self, task_id: Uuid, creator_firebase_id: &str) -> Result<()> {
        // Grant owner permission to the creator
        self.spicedb.grant_resource_permission(
            "task",
            &task_id.to_string(),
            "owner",
            creator_firebase_id,
        ).await?;

        info!("Synced task: {} with owner {}", task_id, creator_firebase_id);
        Ok(())
    }

    /// Check if SpiceDB is available and responsive
    pub async fn health_check(&self) -> bool {
        // Try a simple permission check to verify SpiceDB connectivity
        match self.spicedb.check_permission(
            "global_settings",
            "system",
            "admin",
            "health-check-user",
        ).await {
            Ok(_) => true,
            Err(e) => {
                error!("SpiceDB health check failed: {}", e);
                false
            }
        }
    }

    /// Get synchronization status
    pub async fn get_sync_status(&self) -> Result<SyncStatus> {
        let user_repo = UserRepository::new(self.db.clone());
        let agent_repo = AgentRepository::new(self.db.clone());
        let role_repo = RoleRepository::new(self.db.clone());

        let user_count = user_repo.get_all().await?.len();
        let agent_count = agent_repo.get_all().await?.len();
        let role_count = role_repo.get_all().await?.len();

        let spicedb_healthy = self.health_check().await;

        Ok(SyncStatus {
            user_count,
            agent_count,
            role_count,
            spicedb_healthy,
            last_sync: chrono::Utc::now(),
        })
    }

    /*
    /// Sync role memberships from SpiceDB to SQLite for UI display
    pub async fn sync_role_memberships_to_sqlite(&self) -> Result<()> {
        info!("Syncing role memberships from SpiceDB to SQLite");

        let role_repo = RoleRepository::new(self.db.clone());
        let roles = role_repo.get_all().await?;

        // Add default roles that might not be in SQLite
        let default_roles = vec!["owner", "member"];

        for role_name in default_roles.iter().chain(roles.iter().map(|r| r.name.as_str())) {
            let role_identifier = if role_name == "Owner" || role_name == "Member" {
                role_name.to_lowercase()
            } else {
                role_name.to_string()
            };

            // Clear existing memberships for this role
            if let Err(e) = self.sync_repo.clear_role_memberships(&role_identifier).await {
                warn!("Failed to clear existing memberships for role {}: {}", role_identifier, e);
                continue;
            }

            // Get current members from SpiceDB
            match self.spicedb.get_role_members(&role_identifier).await {
                Ok(members) => {
                    for member in members {
                        let membership = NewRoleMembership {
                            role_id: role_identifier.clone(),
                            member_id: member.id,
                            member_type: member.member_type,
                        };

                        if let Err(e) = self.sync_repo.create_role_membership(membership).await {
                            warn!("Failed to create role membership: {}", e);
                        }
                    }
                    info!("Synced role memberships for role: {}", role_identifier);
                }
                Err(e) => {
                    warn!("Failed to get role members for {}: {}", role_identifier, e);
                }
            }
        }

        Ok(())
    }

    /// Sync permissions from SpiceDB to SQLite for UI display
    pub async fn sync_permissions_to_sqlite(&self) -> Result<()> {
        info!("Syncing permissions from SpiceDB to SQLite");

        // This is a simplified implementation. In a full implementation,
        // you would need to query SpiceDB for all relationships and
        // convert them to permission records in SQLite.

        // For now, we'll sync based on known patterns from our schema
        let user_repo = UserRepository::new(self.db.clone());
        let agent_repo = AgentRepository::new(self.db.clone());

        let users = user_repo.get_all().await?;
        let agents = agent_repo.get_all().await?;

        // Sync user permissions
        for user in users {
            if let Err(e) = self.sync_user_permissions_to_sqlite(&user.user_id).await {
                warn!("Failed to sync permissions for user {}: {}", user.user_id, e);
            }
        }

        // Sync agent permissions
        for agent in agents {
            if let Err(e) = self.sync_agent_permissions_to_sqlite(&agent.id.to_string()).await {
                warn!("Failed to sync permissions for agent {}: {}", agent.id, e);
            }
        }

        Ok(())
    }

    /// Sync permissions for a specific user
    pub async fn sync_user_permissions_to_sqlite(&self, user_id: &str) -> Result<()> {
        // Clear existing permissions
        self.sync_repo.clear_user_permissions(user_id).await?;

        // Get user's roles
        let role_memberships = self.sync_repo.get_member_roles(user_id, "user").await?;

        // Add role-based permissions
        for membership in role_memberships {
            // This is simplified - in practice you'd need to resolve
            // what permissions each role grants
            let permission = NewUserPermission {
                user_id: user_id.to_string(),
                resource_type: "global_settings".to_string(),
                resource_id: "system".to_string(),
                permission: if membership.role_id == "owner" { "admin" } else { "view" }.to_string(),
                granted_via: "role".to_string(),
                granted_via_id: Some(membership.role_id),
            };

            if let Err(e) = self.sync_repo.create_user_permission(permission).await {
                warn!("Failed to create user permission: {}", e);
            }
        }

        Ok(())
    }

    /// Sync permissions for a specific agent
    pub async fn sync_agent_permissions_to_sqlite(&self, agent_id: &str) -> Result<()> {
        // Clear existing permissions
        self.sync_repo.clear_agent_permissions(agent_id).await?;

        // Get agent's roles
        let role_memberships = self.sync_repo.get_member_roles(agent_id, "agent").await?;

        // Add role-based permissions
        for membership in role_memberships {
            let permission = NewAgentPermission {
                agent_id: agent_id.to_string(),
                resource_type: "global_settings".to_string(),
                resource_id: "system".to_string(),
                permission: if membership.role_id == "owner" { "admin" } else { "view" }.to_string(),
                granted_via: "role".to_string(),
                granted_via_id: Some(membership.role_id),
            };

            if let Err(e) = self.sync_repo.create_agent_permission(permission).await {
                warn!("Failed to create agent permission: {}", e);
            }
        }

        Ok(())
    }

    /// Get permission summary for a user (from SQLite for fast UI queries)
    pub async fn get_user_permission_summary(&self, user_id: &str) -> Result<PermissionSummary> {
        let role_repo = RoleRepository::new(self.db.clone());

        // Get user's roles
        let role_memberships = self.sync_repo.get_member_roles(user_id, "user").await?;
        let mut roles = Vec::new();

        for membership in role_memberships {
            // Try to get role details from SQLite
            if let Ok(Some(role)) = role_repo.get_by_name(&membership.role_id).await {
                roles.push(RoleInfo {
                    role_id: membership.role_id,
                    role_name: role.name,
                    role_color: role.color,
                });
            } else {
                // Fallback for default roles
                roles.push(RoleInfo {
                    role_id: membership.role_id.clone(),
                    role_name: membership.role_id,
                    role_color: None,
                });
            }
        }

        // Get direct permissions
        let user_permissions = self.sync_repo.get_user_permissions(user_id).await?;
        let direct_permissions: Vec<PermissionInfo> = user_permissions
            .iter()
            .filter(|p| p.granted_via == "direct")
            .map(|p| PermissionInfo {
                resource_type: p.resource_type.clone(),
                resource_id: p.resource_id.clone(),
                permission: p.permission.clone(),
                granted_via: p.granted_via.clone(),
                granted_via_id: p.granted_via_id.clone(),
            })
            .collect();

        // Get all effective permissions
        let effective_permissions: Vec<PermissionInfo> = user_permissions
            .iter()
            .map(|p| PermissionInfo {
                resource_type: p.resource_type.clone(),
                resource_id: p.resource_id.clone(),
                permission: p.permission.clone(),
                granted_via: p.granted_via.clone(),
                granted_via_id: p.granted_via_id.clone(),
            })
            .collect();

        Ok(PermissionSummary {
            entity_id: user_id.to_string(),
            entity_type: "user".to_string(),
            roles,
            direct_permissions,
            effective_permissions,
        })
    }

    /// Get permission summary for an agent (from SQLite for fast UI queries)
    pub async fn get_agent_permission_summary(&self, agent_id: &str) -> Result<PermissionSummary> {
        let role_repo = RoleRepository::new(self.db.clone());

        // Get agent's roles
        let role_memberships = self.sync_repo.get_member_roles(agent_id, "agent").await?;
        let mut roles = Vec::new();

        for membership in role_memberships {
            if let Ok(Some(role)) = role_repo.get_by_name(&membership.role_id).await {
                roles.push(RoleInfo {
                    role_id: membership.role_id,
                    role_name: role.name,
                    role_color: role.color,
                });
            } else {
                roles.push(RoleInfo {
                    role_id: membership.role_id.clone(),
                    role_name: membership.role_id,
                    role_color: None,
                });
            }
        }

        // Get direct permissions
        let agent_permissions = self.sync_repo.get_agent_permissions(agent_id).await?;
        let direct_permissions: Vec<PermissionInfo> = agent_permissions
            .iter()
            .filter(|p| p.granted_via == "direct")
            .map(|p| PermissionInfo {
                resource_type: p.resource_type.clone(),
                resource_id: p.resource_id.clone(),
                permission: p.permission.clone(),
                granted_via: p.granted_via.clone(),
                granted_via_id: p.granted_via_id.clone(),
            })
            .collect();

        // Get all effective permissions
        let effective_permissions: Vec<PermissionInfo> = agent_permissions
            .iter()
            .map(|p| PermissionInfo {
                resource_type: p.resource_type.clone(),
                resource_id: p.resource_id.clone(),
                permission: p.permission.clone(),
                granted_via: p.granted_via.clone(),
                granted_via_id: p.granted_via_id.clone(),
            })
            .collect();

        Ok(PermissionSummary {
            entity_id: agent_id.to_string(),
            entity_type: "agent".to_string(),
            roles,
            direct_permissions,
            effective_permissions,
        })
    }
    */
}

/// Synchronization status information
#[derive(Debug, Clone)]
pub struct SyncStatus {
    pub user_count: usize,
    pub agent_count: usize,
    pub role_count: usize,
    pub spicedb_healthy: bool,
    pub last_sync: chrono::DateTime<chrono::Utc>,
}

/// Initialize synchronization on server startup
pub async fn initialize_sync(db: Database, spicedb: Option<SpiceDBService>) -> Result<()> {
    if let Some(spicedb_service) = spicedb {
        let sync_service = SyncService::new(db, spicedb_service);
        
        // Check if SpiceDB is available
        if sync_service.health_check().await {
            info!("SpiceDB is available, starting synchronization");
            
            // Perform initial sync
            if let Err(e) = sync_service.sync_all().await {
                error!("Initial synchronization failed: {}", e);
                return Err(e);
            }
            
            info!("Initial synchronization completed successfully");
        } else {
            warn!("SpiceDB is not available, skipping synchronization");
        }
    } else {
        warn!("SpiceDB service not configured, skipping synchronization");
    }

    Ok(())
}
