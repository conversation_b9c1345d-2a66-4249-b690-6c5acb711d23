{"$message_type":"diagnostic","message":"unsupported type NULL of column #1 (\"id\")","code":null,"level":"error","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.8.5\\src\\macros\\mod.rs","byte_start":26699,"byte_end":26792,"line_start":576,"line_end":576,"column_start":9,"column_end":102,"is_primary":true,"text":[{"text":"        $crate::sqlx_macros::expand_query!(record = $out_struct, source = $query, args = [$($args)*])","highlight_start":9,"highlight_end":102}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.8.5\\src\\macros\\mod.rs","byte_start":26699,"byte_end":26792,"line_start":576,"line_end":576,"column_start":9,"column_end":102,"is_primary":false,"text":[{"text":"        $crate::sqlx_macros::expand_query!(record = $out_struct, source = $query, args = [$($args)*])","highlight_start":9,"highlight_end":102}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\db\\repositories\\sync_repository.rs","byte_start":7358,"byte_end":8030,"line_start":196,"line_end":210,"column_start":26,"column_end":14,"is_primary":false,"text":[{"text":"            let result = sqlx::query_as!(","highlight_start":26,"highlight_end":42},{"text":"                UserPermission,","highlight_start":1,"highlight_end":32},{"text":"                r#\"","highlight_start":1,"highlight_end":20},{"text":"                INSERT INTO user_permissions (id, user_id, resource_type, resource_id, permission, granted_via, granted_via_id)","highlight_start":1,"highlight_end":128},{"text":"                VALUES ($1, $2, $3, $4, $5, $6, $7)","highlight_start":1,"highlight_end":52},{"text":"                RETURNING id, user_id, resource_type, resource_id, permission, granted_via, granted_via_id, created_at, updated_at","highlight_start":1,"highlight_end":131},{"text":"                \"#,","highlight_start":1,"highlight_end":20},{"text":"                id,","highlight_start":1,"highlight_end":20},{"text":"                permission.user_id,","highlight_start":1,"highlight_end":36},{"text":"                permission.resource_type,","highlight_start":1,"highlight_end":42},{"text":"                permission.resource_id,","highlight_start":1,"highlight_end":40},{"text":"                permission.permission,","highlight_start":1,"highlight_end":39},{"text":"                permission.granted_via,","highlight_start":1,"highlight_end":40},{"text":"                permission.granted_via_id","highlight_start":1,"highlight_end":42},{"text":"            )","highlight_start":1,"highlight_end":14}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"sqlx::query_as!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.8.5\\src\\macros\\mod.rs","byte_start":26477,"byte_end":26498,"line_start":571,"line_end":571,"column_start":1,"column_end":22,"is_primary":false,"text":[{"text":"macro_rules! query_as (","highlight_start":1,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::sqlx_macros::expand_query!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-macros-0.8.5\\src\\lib.rs","byte_start":116,"byte_end":170,"line_start":9,"line_end":9,"column_start":1,"column_end":55,"is_primary":false,"text":[{"text":"pub fn expand_query(input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":55}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unsupported type NULL of column #1 (\"id\")\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\db\\repositories\\sync_repository.rs:196:26\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m196\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m            let result = sqlx::query_as!(\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m __________________________^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m197\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                UserPermission,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m198\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                r#\"\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m199\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                INSERT INTO user_permissions (id, user_id, resource_type, resource_id, permission, granted_via, granted_via_id)\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m209\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                permission.granted_via_id\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m210\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            )\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|_____________^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query_as` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unsupported type NULL of column #1 (\"id\")","code":null,"level":"error","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.8.5\\src\\macros\\mod.rs","byte_start":26699,"byte_end":26792,"line_start":576,"line_end":576,"column_start":9,"column_end":102,"is_primary":true,"text":[{"text":"        $crate::sqlx_macros::expand_query!(record = $out_struct, source = $query, args = [$($args)*])","highlight_start":9,"highlight_end":102}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.8.5\\src\\macros\\mod.rs","byte_start":26699,"byte_end":26792,"line_start":576,"line_end":576,"column_start":9,"column_end":102,"is_primary":false,"text":[{"text":"        $crate::sqlx_macros::expand_query!(record = $out_struct, source = $query, args = [$($args)*])","highlight_start":9,"highlight_end":102}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\db\\repositories\\sync_repository.rs","byte_start":10844,"byte_end":11156,"line_start":298,"line_end":305,"column_start":31,"column_end":14,"is_primary":false,"text":[{"text":"            let permissions = sqlx::query_as!(","highlight_start":31,"highlight_end":47},{"text":"                UserPermission,","highlight_start":1,"highlight_end":32},{"text":"                r#\"","highlight_start":1,"highlight_end":20},{"text":"                SELECT id, user_id, resource_type, resource_id, permission, granted_via, granted_via_id, created_at, updated_at ","highlight_start":1,"highlight_end":129},{"text":"                FROM user_permissions WHERE user_id = $1","highlight_start":1,"highlight_end":57},{"text":"                \"#,","highlight_start":1,"highlight_end":20},{"text":"                user_id","highlight_start":1,"highlight_end":24},{"text":"            )","highlight_start":1,"highlight_end":14}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"sqlx::query_as!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.8.5\\src\\macros\\mod.rs","byte_start":26477,"byte_end":26498,"line_start":571,"line_end":571,"column_start":1,"column_end":22,"is_primary":false,"text":[{"text":"macro_rules! query_as (","highlight_start":1,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::sqlx_macros::expand_query!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-macros-0.8.5\\src\\lib.rs","byte_start":116,"byte_end":170,"line_start":9,"line_end":9,"column_start":1,"column_end":55,"is_primary":false,"text":[{"text":"pub fn expand_query(input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":55}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unsupported type NULL of column #1 (\"id\")\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\db\\repositories\\sync_repository.rs:298:31\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m298\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m            let permissions = sqlx::query_as!(\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m _______________________________^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m299\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                UserPermission,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m300\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                r#\"\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m301\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                SELECT id, user_id, resource_type, resource_id, permission, granted_via, granted_via_id, created_at, updated_at \u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m304\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                user_id\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m305\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            )\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|_____________^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query_as` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unsupported type NULL of column #1 (\"id\")","code":null,"level":"error","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.8.5\\src\\macros\\mod.rs","byte_start":26699,"byte_end":26792,"line_start":576,"line_end":576,"column_start":9,"column_end":102,"is_primary":true,"text":[{"text":"        $crate::sqlx_macros::expand_query!(record = $out_struct, source = $query, args = [$($args)*])","highlight_start":9,"highlight_end":102}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.8.5\\src\\macros\\mod.rs","byte_start":26699,"byte_end":26792,"line_start":576,"line_end":576,"column_start":9,"column_end":102,"is_primary":false,"text":[{"text":"        $crate::sqlx_macros::expand_query!(record = $out_struct, source = $query, args = [$($args)*])","highlight_start":9,"highlight_end":102}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\db\\repositories\\sync_repository.rs","byte_start":12637,"byte_end":13314,"line_start":359,"line_end":373,"column_start":26,"column_end":14,"is_primary":false,"text":[{"text":"            let result = sqlx::query_as!(","highlight_start":26,"highlight_end":42},{"text":"                AgentPermission,","highlight_start":1,"highlight_end":33},{"text":"                r#\"","highlight_start":1,"highlight_end":20},{"text":"                INSERT INTO agent_permissions (id, agent_id, resource_type, resource_id, permission, granted_via, granted_via_id)","highlight_start":1,"highlight_end":130},{"text":"                VALUES ($1, $2, $3, $4, $5, $6, $7)","highlight_start":1,"highlight_end":52},{"text":"                RETURNING id, agent_id, resource_type, resource_id, permission, granted_via, granted_via_id, created_at, updated_at","highlight_start":1,"highlight_end":132},{"text":"                \"#,","highlight_start":1,"highlight_end":20},{"text":"                id,","highlight_start":1,"highlight_end":20},{"text":"                permission.agent_id,","highlight_start":1,"highlight_end":37},{"text":"                permission.resource_type,","highlight_start":1,"highlight_end":42},{"text":"                permission.resource_id,","highlight_start":1,"highlight_end":40},{"text":"                permission.permission,","highlight_start":1,"highlight_end":39},{"text":"                permission.granted_via,","highlight_start":1,"highlight_end":40},{"text":"                permission.granted_via_id","highlight_start":1,"highlight_end":42},{"text":"            )","highlight_start":1,"highlight_end":14}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"sqlx::query_as!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.8.5\\src\\macros\\mod.rs","byte_start":26477,"byte_end":26498,"line_start":571,"line_end":571,"column_start":1,"column_end":22,"is_primary":false,"text":[{"text":"macro_rules! query_as (","highlight_start":1,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::sqlx_macros::expand_query!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-macros-0.8.5\\src\\lib.rs","byte_start":116,"byte_end":170,"line_start":9,"line_end":9,"column_start":1,"column_end":55,"is_primary":false,"text":[{"text":"pub fn expand_query(input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":55}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unsupported type NULL of column #1 (\"id\")\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\db\\repositories\\sync_repository.rs:359:26\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m359\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m            let result = sqlx::query_as!(\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m __________________________^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m360\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                AgentPermission,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m361\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                r#\"\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m362\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                INSERT INTO agent_permissions (id, agent_id, resource_type, resource_id, permission, granted_via, granted_via_id)\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m372\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                permission.granted_via_id\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m373\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            )\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|_____________^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query_as` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unsupported type NULL of column #1 (\"id\")","code":null,"level":"error","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.8.5\\src\\macros\\mod.rs","byte_start":26699,"byte_end":26792,"line_start":576,"line_end":576,"column_start":9,"column_end":102,"is_primary":true,"text":[{"text":"        $crate::sqlx_macros::expand_query!(record = $out_struct, source = $query, args = [$($args)*])","highlight_start":9,"highlight_end":102}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.8.5\\src\\macros\\mod.rs","byte_start":26699,"byte_end":26792,"line_start":576,"line_end":576,"column_start":9,"column_end":102,"is_primary":false,"text":[{"text":"        $crate::sqlx_macros::expand_query!(record = $out_struct, source = $query, args = [$($args)*])","highlight_start":9,"highlight_end":102}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\db\\repositories\\sync_repository.rs","byte_start":16074,"byte_end":16390,"line_start":461,"line_end":468,"column_start":31,"column_end":14,"is_primary":false,"text":[{"text":"            let permissions = sqlx::query_as!(","highlight_start":31,"highlight_end":47},{"text":"                AgentPermission,","highlight_start":1,"highlight_end":33},{"text":"                r#\"","highlight_start":1,"highlight_end":20},{"text":"                SELECT id, agent_id, resource_type, resource_id, permission, granted_via, granted_via_id, created_at, updated_at","highlight_start":1,"highlight_end":129},{"text":"                FROM agent_permissions WHERE agent_id = $1","highlight_start":1,"highlight_end":59},{"text":"                \"#,","highlight_start":1,"highlight_end":20},{"text":"                agent_id","highlight_start":1,"highlight_end":25},{"text":"            )","highlight_start":1,"highlight_end":14}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"sqlx::query_as!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.8.5\\src\\macros\\mod.rs","byte_start":26477,"byte_end":26498,"line_start":571,"line_end":571,"column_start":1,"column_end":22,"is_primary":false,"text":[{"text":"macro_rules! query_as (","highlight_start":1,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::sqlx_macros::expand_query!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-macros-0.8.5\\src\\lib.rs","byte_start":116,"byte_end":170,"line_start":9,"line_end":9,"column_start":1,"column_end":55,"is_primary":false,"text":[{"text":"pub fn expand_query(input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":55}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unsupported type NULL of column #1 (\"id\")\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\db\\repositories\\sync_repository.rs:461:31\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m461\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m            let permissions = sqlx::query_as!(\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m _______________________________^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m462\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                AgentPermission,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m463\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                r#\"\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m464\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                SELECT id, agent_id, resource_type, resource_id, permission, granted_via, granted_via_id, created_at, updated_at\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m467\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                agent_id\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m468\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            )\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|_____________^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query_as` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `Path`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\api\\handlers\\sync_handlers.rs","byte_start":26,"byte_end":30,"line_start":2,"line_end":2,"column_start":15,"column_end":19,"is_primary":true,"text":[{"text":"    extract::{Path, State},","highlight_start":15,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_imports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src\\api\\handlers\\sync_handlers.rs","byte_start":26,"byte_end":32,"line_start":2,"line_end":2,"column_start":15,"column_end":21,"is_primary":true,"text":[{"text":"    extract::{Path, State},","highlight_start":15,"highlight_end":21}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\api\\handlers\\sync_handlers.rs","byte_start":25,"byte_end":26,"line_start":2,"line_end":2,"column_start":14,"column_end":15,"is_primary":true,"text":[{"text":"    extract::{Path, State},","highlight_start":14,"highlight_end":15}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\api\\handlers\\sync_handlers.rs","byte_start":37,"byte_end":38,"line_start":2,"line_end":2,"column_start":26,"column_end":27,"is_primary":true,"text":[{"text":"    extract::{Path, State},","highlight_start":26,"highlight_end":27}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `Path`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\api\\handlers\\sync_handlers.rs:2:15\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m2\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    extract::{Path, State},\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m               \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(unused_imports)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `uuid::Uuid`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\api\\handlers\\sync_handlers.rs","byte_start":116,"byte_end":126,"line_start":7,"line_end":7,"column_start":5,"column_end":15,"is_primary":true,"text":[{"text":"use uuid::Uuid;","highlight_start":5,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\api\\handlers\\sync_handlers.rs","byte_start":112,"byte_end":128,"line_start":7,"line_end":8,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use uuid::Uuid;","highlight_start":1,"highlight_end":16},{"text":"use crate::{","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `uuid::Uuid`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\api\\handlers\\sync_handlers.rs:7:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m7\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse uuid::Uuid;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"the trait bound `(): sqlx::Decode<'_, Sqlite>` is not satisfied","code":{"code":"E0277","explanation":"You tried to use a type which doesn't implement some trait in a place which\nexpected that trait.\n\nErroneous code example:\n\n```compile_fail,E0277\n// here we declare the Foo trait with a bar method\ntrait Foo {\n    fn bar(&self);\n}\n\n// we now declare a function which takes an object implementing the Foo trait\nfn some_func<T: Foo>(foo: T) {\n    foo.bar();\n}\n\nfn main() {\n    // we now call the method with the i32 type, which doesn't implement\n    // the Foo trait\n    some_func(5i32); // error: the trait bound `i32 : Foo` is not satisfied\n}\n```\n\nIn order to fix this error, verify that the type you're using does implement\nthe trait. Example:\n\n```\ntrait Foo {\n    fn bar(&self);\n}\n\n// we implement the trait on the i32 type\nimpl Foo for i32 {\n    fn bar(&self) {}\n}\n\nfn some_func<T: Foo>(foo: T) {\n    foo.bar(); // we can now use this method since i32 implements the\n               // Foo trait\n}\n\nfn main() {\n    some_func(5i32); // ok!\n}\n```\n\nOr in a generic context, an erroneous code example would look like:\n\n```compile_fail,E0277\nfn some_func<T>(foo: T) {\n    println!(\"{:?}\", foo); // error: the trait `core::fmt::Debug` is not\n                           //        implemented for the type `T`\n}\n\nfn main() {\n    // We now call the method with the i32 type,\n    // which *does* implement the Debug trait.\n    some_func(5i32);\n}\n```\n\nNote that the error here is in the definition of the generic function. Although\nwe only call it with a parameter that does implement `Debug`, the compiler\nstill rejects the function. It must work with all possible input types. In\norder to make this example compile, we need to restrict the generic type we're\naccepting:\n\n```\nuse std::fmt;\n\n// Restrict the input type to types that implement Debug.\nfn some_func<T: fmt::Debug>(foo: T) {\n    println!(\"{:?}\", foo);\n}\n\nfn main() {\n    // Calling the method is still fine, as i32 implements Debug.\n    some_func(5i32);\n\n    // This would fail to compile now:\n    // struct WithoutDebug;\n    // some_func(WithoutDebug);\n}\n```\n\nRust only looks at the signature of the called function, as such it must\nalready specify all requirements that will be used for every type parameter.\n"},"level":"error","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.8.5\\src\\macros\\mod.rs","byte_start":26699,"byte_end":26792,"line_start":576,"line_end":576,"column_start":9,"column_end":102,"is_primary":true,"text":[{"text":"        $crate::sqlx_macros::expand_query!(record = $out_struct, source = $query, args = [$($args)*])","highlight_start":9,"highlight_end":102}],"label":"the trait `sqlx::Decode<'_, Sqlite>` is not implemented for `()`","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.8.5\\src\\macros\\mod.rs","byte_start":26699,"byte_end":26792,"line_start":576,"line_end":576,"column_start":9,"column_end":102,"is_primary":false,"text":[{"text":"        $crate::sqlx_macros::expand_query!(record = $out_struct, source = $query, args = [$($args)*])","highlight_start":9,"highlight_end":102}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\db\\repositories\\sync_repository.rs","byte_start":7358,"byte_end":8030,"line_start":196,"line_end":210,"column_start":26,"column_end":14,"is_primary":false,"text":[{"text":"            let result = sqlx::query_as!(","highlight_start":26,"highlight_end":42},{"text":"                UserPermission,","highlight_start":1,"highlight_end":32},{"text":"                r#\"","highlight_start":1,"highlight_end":20},{"text":"                INSERT INTO user_permissions (id, user_id, resource_type, resource_id, permission, granted_via, granted_via_id)","highlight_start":1,"highlight_end":128},{"text":"                VALUES ($1, $2, $3, $4, $5, $6, $7)","highlight_start":1,"highlight_end":52},{"text":"                RETURNING id, user_id, resource_type, resource_id, permission, granted_via, granted_via_id, created_at, updated_at","highlight_start":1,"highlight_end":131},{"text":"                \"#,","highlight_start":1,"highlight_end":20},{"text":"                id,","highlight_start":1,"highlight_end":20},{"text":"                permission.user_id,","highlight_start":1,"highlight_end":36},{"text":"                permission.resource_type,","highlight_start":1,"highlight_end":42},{"text":"                permission.resource_id,","highlight_start":1,"highlight_end":40},{"text":"                permission.permission,","highlight_start":1,"highlight_end":39},{"text":"                permission.granted_via,","highlight_start":1,"highlight_end":40},{"text":"                permission.granted_via_id","highlight_start":1,"highlight_end":42},{"text":"            )","highlight_start":1,"highlight_end":14}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"sqlx::query_as!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.8.5\\src\\macros\\mod.rs","byte_start":26477,"byte_end":26498,"line_start":571,"line_end":571,"column_start":1,"column_end":22,"is_primary":false,"text":[{"text":"macro_rules! query_as (","highlight_start":1,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::sqlx_macros::expand_query!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-macros-0.8.5\\src\\lib.rs","byte_start":116,"byte_end":170,"line_start":9,"line_end":9,"column_start":1,"column_end":55,"is_primary":false,"text":[{"text":"pub fn expand_query(input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":55}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[{"message":"the following other types implement trait `sqlx::Decode<'r, DB>`:\n  `&'a str` implements `sqlx::Decode<'a, sqlx::Any>`\n  `&'r JsonRawValue` implements `sqlx::Decode<'r, DB>`\n  `&'r [u8]` implements `sqlx::Decode<'r, Sqlite>`\n  `&'r [u8]` implements `sqlx::Decode<'r, sqlx::Any>`\n  `&'r str` implements `sqlx::Decode<'r, Sqlite>`\n  `Box<[u8]>` implements `sqlx::Decode<'_, Sqlite>`\n  `Box<str>` implements `sqlx::Decode<'_, Sqlite>`\n  `Cow<'r, str>` implements `sqlx::Decode<'r, Sqlite>`\nand 46 others","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"required by a bound in `try_get_unchecked`","code":null,"level":"note","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-core-0.8.5\\src\\row.rs","byte_start":5166,"byte_end":5183,"line_start":152,"line_end":152,"column_start":8,"column_end":25,"is_primary":false,"text":[{"text":"    fn try_get_unchecked<'r, T, I>(&'r self, index: I) -> Result<T, Error>","highlight_start":8,"highlight_end":25}],"label":"required by a bound in this associated function","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-core-0.8.5\\src\\row.rs","byte_start":5285,"byte_end":5311,"line_start":155,"line_end":155,"column_start":12,"column_end":38,"is_primary":true,"text":[{"text":"        T: Decode<'r, Self::Database>,","highlight_start":12,"highlight_end":38}],"label":"required by this bound in `Row::try_get_unchecked`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0277]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: the trait bound `(): sqlx::Decode<'_, Sqlite>` is not satisfied\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\db\\repositories\\sync_repository.rs:196:26\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m196\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m            let result = sqlx::query_as!(\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m __________________________^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m197\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                UserPermission,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m198\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                r#\"\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m199\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                INSERT INTO user_permissions (id, user_id, resource_type, resource_id, permission, granted_via, granted_via_id)\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m209\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                permission.granted_via_id\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m210\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            )\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|_____________^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mthe trait `sqlx::Decode<'_, Sqlite>` is not implemented for `()`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: the following other types implement trait `sqlx::Decode<'r, DB>`:\u001b[0m\n\u001b[0m              `&'a str` implements `sqlx::Decode<'a, sqlx::Any>`\u001b[0m\n\u001b[0m              `&'r JsonRawValue` implements `sqlx::Decode<'r, DB>`\u001b[0m\n\u001b[0m              `&'r [u8]` implements `sqlx::Decode<'r, Sqlite>`\u001b[0m\n\u001b[0m              `&'r [u8]` implements `sqlx::Decode<'r, sqlx::Any>`\u001b[0m\n\u001b[0m              `&'r str` implements `sqlx::Decode<'r, Sqlite>`\u001b[0m\n\u001b[0m              `Box<[u8]>` implements `sqlx::Decode<'_, Sqlite>`\u001b[0m\n\u001b[0m              `Box<str>` implements `sqlx::Decode<'_, Sqlite>`\u001b[0m\n\u001b[0m              `Cow<'r, str>` implements `sqlx::Decode<'r, Sqlite>`\u001b[0m\n\u001b[0m            and 46 others\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: required by a bound in `try_get_unchecked`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-core-0.8.5\\src\\row.rs:155:12\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m152\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn try_get_unchecked<'r, T, I>(&'r self, index: I) -> Result<T, Error>\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-----------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mrequired by a bound in this associated function\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m155\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        T: Decode<'r, Self::Database>,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10mrequired by this bound in `Row::try_get_unchecked`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query_as` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"the trait bound `uuid::Uuid: From<()>` is not satisfied","code":{"code":"E0277","explanation":"You tried to use a type which doesn't implement some trait in a place which\nexpected that trait.\n\nErroneous code example:\n\n```compile_fail,E0277\n// here we declare the Foo trait with a bar method\ntrait Foo {\n    fn bar(&self);\n}\n\n// we now declare a function which takes an object implementing the Foo trait\nfn some_func<T: Foo>(foo: T) {\n    foo.bar();\n}\n\nfn main() {\n    // we now call the method with the i32 type, which doesn't implement\n    // the Foo trait\n    some_func(5i32); // error: the trait bound `i32 : Foo` is not satisfied\n}\n```\n\nIn order to fix this error, verify that the type you're using does implement\nthe trait. Example:\n\n```\ntrait Foo {\n    fn bar(&self);\n}\n\n// we implement the trait on the i32 type\nimpl Foo for i32 {\n    fn bar(&self) {}\n}\n\nfn some_func<T: Foo>(foo: T) {\n    foo.bar(); // we can now use this method since i32 implements the\n               // Foo trait\n}\n\nfn main() {\n    some_func(5i32); // ok!\n}\n```\n\nOr in a generic context, an erroneous code example would look like:\n\n```compile_fail,E0277\nfn some_func<T>(foo: T) {\n    println!(\"{:?}\", foo); // error: the trait `core::fmt::Debug` is not\n                           //        implemented for the type `T`\n}\n\nfn main() {\n    // We now call the method with the i32 type,\n    // which *does* implement the Debug trait.\n    some_func(5i32);\n}\n```\n\nNote that the error here is in the definition of the generic function. Although\nwe only call it with a parameter that does implement `Debug`, the compiler\nstill rejects the function. It must work with all possible input types. In\norder to make this example compile, we need to restrict the generic type we're\naccepting:\n\n```\nuse std::fmt;\n\n// Restrict the input type to types that implement Debug.\nfn some_func<T: fmt::Debug>(foo: T) {\n    println!(\"{:?}\", foo);\n}\n\nfn main() {\n    // Calling the method is still fine, as i32 implements Debug.\n    some_func(5i32);\n\n    // This would fail to compile now:\n    // struct WithoutDebug;\n    // some_func(WithoutDebug);\n}\n```\n\nRust only looks at the signature of the called function, as such it must\nalready specify all requirements that will be used for every type parameter.\n"},"level":"error","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.8.5\\src\\macros\\mod.rs","byte_start":26699,"byte_end":26792,"line_start":576,"line_end":576,"column_start":9,"column_end":102,"is_primary":true,"text":[{"text":"        $crate::sqlx_macros::expand_query!(record = $out_struct, source = $query, args = [$($args)*])","highlight_start":9,"highlight_end":102}],"label":"the trait `From<()>` is not implemented for `uuid::Uuid`","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.8.5\\src\\macros\\mod.rs","byte_start":26699,"byte_end":26792,"line_start":576,"line_end":576,"column_start":9,"column_end":102,"is_primary":false,"text":[{"text":"        $crate::sqlx_macros::expand_query!(record = $out_struct, source = $query, args = [$($args)*])","highlight_start":9,"highlight_end":102}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\db\\repositories\\sync_repository.rs","byte_start":7358,"byte_end":8030,"line_start":196,"line_end":210,"column_start":26,"column_end":14,"is_primary":false,"text":[{"text":"            let result = sqlx::query_as!(","highlight_start":26,"highlight_end":42},{"text":"                UserPermission,","highlight_start":1,"highlight_end":32},{"text":"                r#\"","highlight_start":1,"highlight_end":20},{"text":"                INSERT INTO user_permissions (id, user_id, resource_type, resource_id, permission, granted_via, granted_via_id)","highlight_start":1,"highlight_end":128},{"text":"                VALUES ($1, $2, $3, $4, $5, $6, $7)","highlight_start":1,"highlight_end":52},{"text":"                RETURNING id, user_id, resource_type, resource_id, permission, granted_via, granted_via_id, created_at, updated_at","highlight_start":1,"highlight_end":131},{"text":"                \"#,","highlight_start":1,"highlight_end":20},{"text":"                id,","highlight_start":1,"highlight_end":20},{"text":"                permission.user_id,","highlight_start":1,"highlight_end":36},{"text":"                permission.resource_type,","highlight_start":1,"highlight_end":42},{"text":"                permission.resource_id,","highlight_start":1,"highlight_end":40},{"text":"                permission.permission,","highlight_start":1,"highlight_end":39},{"text":"                permission.granted_via,","highlight_start":1,"highlight_end":40},{"text":"                permission.granted_via_id","highlight_start":1,"highlight_end":42},{"text":"            )","highlight_start":1,"highlight_end":14}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"sqlx::query_as!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.8.5\\src\\macros\\mod.rs","byte_start":26477,"byte_end":26498,"line_start":571,"line_end":571,"column_start":1,"column_end":22,"is_primary":false,"text":[{"text":"macro_rules! query_as (","highlight_start":1,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::sqlx_macros::expand_query!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-macros-0.8.5\\src\\lib.rs","byte_start":116,"byte_end":170,"line_start":9,"line_end":9,"column_start":1,"column_end":55,"is_primary":false,"text":[{"text":"pub fn expand_query(input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":55}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[{"message":"the following other types implement trait `From<T>`:\n  `uuid::Uuid` implements `From<Braced>`\n  `uuid::Uuid` implements `From<Hyphenated>`\n  `uuid::Uuid` implements `From<NonNilUuid>`\n  `uuid::Uuid` implements `From<Urn>`\n  `uuid::Uuid` implements `From<uuid::fmt::Simple>`","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"required for `()` to implement `Into<uuid::Uuid>`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0277]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: the trait bound `uuid::Uuid: From<()>` is not satisfied\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\db\\repositories\\sync_repository.rs:196:26\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m196\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m            let result = sqlx::query_as!(\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m __________________________^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m197\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                UserPermission,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m198\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                r#\"\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m199\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                INSERT INTO user_permissions (id, user_id, resource_type, resource_id, permission, granted_via, granted_via_id)\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m209\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                permission.granted_via_id\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m210\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            )\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|_____________^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mthe trait `From<()>` is not implemented for `uuid::Uuid`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: the following other types implement trait `From<T>`:\u001b[0m\n\u001b[0m              `uuid::Uuid` implements `From<Braced>`\u001b[0m\n\u001b[0m              `uuid::Uuid` implements `From<Hyphenated>`\u001b[0m\n\u001b[0m              `uuid::Uuid` implements `From<NonNilUuid>`\u001b[0m\n\u001b[0m              `uuid::Uuid` implements `From<Urn>`\u001b[0m\n\u001b[0m              `uuid::Uuid` implements `From<uuid::fmt::Simple>`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: required for `()` to implement `Into<uuid::Uuid>`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query_as` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"the trait bound `chrono::DateTime<Utc>: From<NaiveDateTime>` is not satisfied","code":{"code":"E0277","explanation":"You tried to use a type which doesn't implement some trait in a place which\nexpected that trait.\n\nErroneous code example:\n\n```compile_fail,E0277\n// here we declare the Foo trait with a bar method\ntrait Foo {\n    fn bar(&self);\n}\n\n// we now declare a function which takes an object implementing the Foo trait\nfn some_func<T: Foo>(foo: T) {\n    foo.bar();\n}\n\nfn main() {\n    // we now call the method with the i32 type, which doesn't implement\n    // the Foo trait\n    some_func(5i32); // error: the trait bound `i32 : Foo` is not satisfied\n}\n```\n\nIn order to fix this error, verify that the type you're using does implement\nthe trait. Example:\n\n```\ntrait Foo {\n    fn bar(&self);\n}\n\n// we implement the trait on the i32 type\nimpl Foo for i32 {\n    fn bar(&self) {}\n}\n\nfn some_func<T: Foo>(foo: T) {\n    foo.bar(); // we can now use this method since i32 implements the\n               // Foo trait\n}\n\nfn main() {\n    some_func(5i32); // ok!\n}\n```\n\nOr in a generic context, an erroneous code example would look like:\n\n```compile_fail,E0277\nfn some_func<T>(foo: T) {\n    println!(\"{:?}\", foo); // error: the trait `core::fmt::Debug` is not\n                           //        implemented for the type `T`\n}\n\nfn main() {\n    // We now call the method with the i32 type,\n    // which *does* implement the Debug trait.\n    some_func(5i32);\n}\n```\n\nNote that the error here is in the definition of the generic function. Although\nwe only call it with a parameter that does implement `Debug`, the compiler\nstill rejects the function. It must work with all possible input types. In\norder to make this example compile, we need to restrict the generic type we're\naccepting:\n\n```\nuse std::fmt;\n\n// Restrict the input type to types that implement Debug.\nfn some_func<T: fmt::Debug>(foo: T) {\n    println!(\"{:?}\", foo);\n}\n\nfn main() {\n    // Calling the method is still fine, as i32 implements Debug.\n    some_func(5i32);\n\n    // This would fail to compile now:\n    // struct WithoutDebug;\n    // some_func(WithoutDebug);\n}\n```\n\nRust only looks at the signature of the called function, as such it must\nalready specify all requirements that will be used for every type parameter.\n"},"level":"error","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.8.5\\src\\macros\\mod.rs","byte_start":26699,"byte_end":26792,"line_start":576,"line_end":576,"column_start":9,"column_end":102,"is_primary":true,"text":[{"text":"        $crate::sqlx_macros::expand_query!(record = $out_struct, source = $query, args = [$($args)*])","highlight_start":9,"highlight_end":102}],"label":"the trait `From<NaiveDateTime>` is not implemented for `chrono::DateTime<Utc>`","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.8.5\\src\\macros\\mod.rs","byte_start":26699,"byte_end":26792,"line_start":576,"line_end":576,"column_start":9,"column_end":102,"is_primary":false,"text":[{"text":"        $crate::sqlx_macros::expand_query!(record = $out_struct, source = $query, args = [$($args)*])","highlight_start":9,"highlight_end":102}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\db\\repositories\\sync_repository.rs","byte_start":7358,"byte_end":8030,"line_start":196,"line_end":210,"column_start":26,"column_end":14,"is_primary":false,"text":[{"text":"            let result = sqlx::query_as!(","highlight_start":26,"highlight_end":42},{"text":"                UserPermission,","highlight_start":1,"highlight_end":32},{"text":"                r#\"","highlight_start":1,"highlight_end":20},{"text":"                INSERT INTO user_permissions (id, user_id, resource_type, resource_id, permission, granted_via, granted_via_id)","highlight_start":1,"highlight_end":128},{"text":"                VALUES ($1, $2, $3, $4, $5, $6, $7)","highlight_start":1,"highlight_end":52},{"text":"                RETURNING id, user_id, resource_type, resource_id, permission, granted_via, granted_via_id, created_at, updated_at","highlight_start":1,"highlight_end":131},{"text":"                \"#,","highlight_start":1,"highlight_end":20},{"text":"                id,","highlight_start":1,"highlight_end":20},{"text":"                permission.user_id,","highlight_start":1,"highlight_end":36},{"text":"                permission.resource_type,","highlight_start":1,"highlight_end":42},{"text":"                permission.resource_id,","highlight_start":1,"highlight_end":40},{"text":"                permission.permission,","highlight_start":1,"highlight_end":39},{"text":"                permission.granted_via,","highlight_start":1,"highlight_end":40},{"text":"                permission.granted_via_id","highlight_start":1,"highlight_end":42},{"text":"            )","highlight_start":1,"highlight_end":14}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"sqlx::query_as!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.8.5\\src\\macros\\mod.rs","byte_start":26477,"byte_end":26498,"line_start":571,"line_end":571,"column_start":1,"column_end":22,"is_primary":false,"text":[{"text":"macro_rules! query_as (","highlight_start":1,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::sqlx_macros::expand_query!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-macros-0.8.5\\src\\lib.rs","byte_start":116,"byte_end":170,"line_start":9,"line_end":9,"column_start":1,"column_end":55,"is_primary":false,"text":[{"text":"pub fn expand_query(input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":55}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[{"message":"the following other types implement trait `From<T>`:\n  `chrono::DateTime<Utc>` implements `From<chrono::DateTime<FixedOffset>>`\n  `chrono::DateTime<Utc>` implements `From<chrono::DateTime<Local>>`\n  `chrono::DateTime<Utc>` implements `From<std::time::SystemTime>`","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"required for `NaiveDateTime` to implement `Into<chrono::DateTime<Utc>>`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0277]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: the trait bound `chrono::DateTime<Utc>: From<NaiveDateTime>` is not satisfied\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\db\\repositories\\sync_repository.rs:196:26\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m196\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m            let result = sqlx::query_as!(\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m __________________________^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m197\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                UserPermission,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m198\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                r#\"\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m199\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                INSERT INTO user_permissions (id, user_id, resource_type, resource_id, permission, granted_via, granted_via_id)\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m209\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                permission.granted_via_id\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m210\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            )\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|_____________^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mthe trait `From<NaiveDateTime>` is not implemented for `chrono::DateTime<Utc>`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: the following other types implement trait `From<T>`:\u001b[0m\n\u001b[0m              `chrono::DateTime<Utc>` implements `From<chrono::DateTime<FixedOffset>>`\u001b[0m\n\u001b[0m              `chrono::DateTime<Utc>` implements `From<chrono::DateTime<Local>>`\u001b[0m\n\u001b[0m              `chrono::DateTime<Utc>` implements `From<std::time::SystemTime>`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: required for `NaiveDateTime` to implement `Into<chrono::DateTime<Utc>>`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query_as` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no field `pool` on type `connection::Database`","code":{"code":"E0609","explanation":"Attempted to access a nonexistent field in a struct.\n\nErroneous code example:\n\n```compile_fail,E0609\nstruct StructWithFields {\n    x: u32,\n}\n\nlet s = StructWithFields { x: 0 };\nprintln!(\"{}\", s.foo); // error: no field `foo` on type `StructWithFields`\n```\n\nTo fix this error, check that you didn't misspell the field's name or that the\nfield actually exists. Example:\n\n```\nstruct StructWithFields {\n    x: u32,\n}\n\nlet s = StructWithFields { x: 0 };\nprintln!(\"{}\", s.x); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\db\\repositories\\sync_repository.rs","byte_start":8063,"byte_end":8067,"line_start":211,"line_end":211,"column_start":33,"column_end":37,"is_primary":true,"text":[{"text":"            .fetch_one(&self.db.pool)","highlight_start":33,"highlight_end":37}],"label":"unknown field","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"available field is: `connection`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0609]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: no field `pool` on type `connection::Database`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\db\\repositories\\sync_repository.rs:211:33\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m211\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            .fetch_one(&self.db.pool)\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9munknown field\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: available field is: `connection`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no field `pool` on type `connection::Database`","code":{"code":"E0609","explanation":"Attempted to access a nonexistent field in a struct.\n\nErroneous code example:\n\n```compile_fail,E0609\nstruct StructWithFields {\n    x: u32,\n}\n\nlet s = StructWithFields { x: 0 };\nprintln!(\"{}\", s.foo); // error: no field `foo` on type `StructWithFields`\n```\n\nTo fix this error, check that you didn't misspell the field's name or that the\nfield actually exists. Example:\n\n```\nstruct StructWithFields {\n    x: u32,\n}\n\nlet s = StructWithFields { x: 0 };\nprintln!(\"{}\", s.x); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\db\\repositories\\sync_repository.rs","byte_start":9862,"byte_end":9866,"line_start":266,"line_end":266,"column_start":31,"column_end":35,"is_primary":true,"text":[{"text":"            .execute(&self.db.pool)","highlight_start":31,"highlight_end":35}],"label":"unknown field","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"available field is: `connection`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0609]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: no field `pool` on type `connection::Database`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\db\\repositories\\sync_repository.rs:266:31\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m266\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            .execute(&self.db.pool)\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                               \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9munknown field\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: available field is: `connection`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"the trait bound `(): sqlx::Decode<'_, Sqlite>` is not satisfied","code":{"code":"E0277","explanation":"You tried to use a type which doesn't implement some trait in a place which\nexpected that trait.\n\nErroneous code example:\n\n```compile_fail,E0277\n// here we declare the Foo trait with a bar method\ntrait Foo {\n    fn bar(&self);\n}\n\n// we now declare a function which takes an object implementing the Foo trait\nfn some_func<T: Foo>(foo: T) {\n    foo.bar();\n}\n\nfn main() {\n    // we now call the method with the i32 type, which doesn't implement\n    // the Foo trait\n    some_func(5i32); // error: the trait bound `i32 : Foo` is not satisfied\n}\n```\n\nIn order to fix this error, verify that the type you're using does implement\nthe trait. Example:\n\n```\ntrait Foo {\n    fn bar(&self);\n}\n\n// we implement the trait on the i32 type\nimpl Foo for i32 {\n    fn bar(&self) {}\n}\n\nfn some_func<T: Foo>(foo: T) {\n    foo.bar(); // we can now use this method since i32 implements the\n               // Foo trait\n}\n\nfn main() {\n    some_func(5i32); // ok!\n}\n```\n\nOr in a generic context, an erroneous code example would look like:\n\n```compile_fail,E0277\nfn some_func<T>(foo: T) {\n    println!(\"{:?}\", foo); // error: the trait `core::fmt::Debug` is not\n                           //        implemented for the type `T`\n}\n\nfn main() {\n    // We now call the method with the i32 type,\n    // which *does* implement the Debug trait.\n    some_func(5i32);\n}\n```\n\nNote that the error here is in the definition of the generic function. Although\nwe only call it with a parameter that does implement `Debug`, the compiler\nstill rejects the function. It must work with all possible input types. In\norder to make this example compile, we need to restrict the generic type we're\naccepting:\n\n```\nuse std::fmt;\n\n// Restrict the input type to types that implement Debug.\nfn some_func<T: fmt::Debug>(foo: T) {\n    println!(\"{:?}\", foo);\n}\n\nfn main() {\n    // Calling the method is still fine, as i32 implements Debug.\n    some_func(5i32);\n\n    // This would fail to compile now:\n    // struct WithoutDebug;\n    // some_func(WithoutDebug);\n}\n```\n\nRust only looks at the signature of the called function, as such it must\nalready specify all requirements that will be used for every type parameter.\n"},"level":"error","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.8.5\\src\\macros\\mod.rs","byte_start":26699,"byte_end":26792,"line_start":576,"line_end":576,"column_start":9,"column_end":102,"is_primary":true,"text":[{"text":"        $crate::sqlx_macros::expand_query!(record = $out_struct, source = $query, args = [$($args)*])","highlight_start":9,"highlight_end":102}],"label":"the trait `sqlx::Decode<'_, Sqlite>` is not implemented for `()`","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.8.5\\src\\macros\\mod.rs","byte_start":26699,"byte_end":26792,"line_start":576,"line_end":576,"column_start":9,"column_end":102,"is_primary":false,"text":[{"text":"        $crate::sqlx_macros::expand_query!(record = $out_struct, source = $query, args = [$($args)*])","highlight_start":9,"highlight_end":102}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\db\\repositories\\sync_repository.rs","byte_start":10844,"byte_end":11156,"line_start":298,"line_end":305,"column_start":31,"column_end":14,"is_primary":false,"text":[{"text":"            let permissions = sqlx::query_as!(","highlight_start":31,"highlight_end":47},{"text":"                UserPermission,","highlight_start":1,"highlight_end":32},{"text":"                r#\"","highlight_start":1,"highlight_end":20},{"text":"                SELECT id, user_id, resource_type, resource_id, permission, granted_via, granted_via_id, created_at, updated_at ","highlight_start":1,"highlight_end":129},{"text":"                FROM user_permissions WHERE user_id = $1","highlight_start":1,"highlight_end":57},{"text":"                \"#,","highlight_start":1,"highlight_end":20},{"text":"                user_id","highlight_start":1,"highlight_end":24},{"text":"            )","highlight_start":1,"highlight_end":14}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"sqlx::query_as!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.8.5\\src\\macros\\mod.rs","byte_start":26477,"byte_end":26498,"line_start":571,"line_end":571,"column_start":1,"column_end":22,"is_primary":false,"text":[{"text":"macro_rules! query_as (","highlight_start":1,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::sqlx_macros::expand_query!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-macros-0.8.5\\src\\lib.rs","byte_start":116,"byte_end":170,"line_start":9,"line_end":9,"column_start":1,"column_end":55,"is_primary":false,"text":[{"text":"pub fn expand_query(input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":55}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[{"message":"the following other types implement trait `sqlx::Decode<'r, DB>`:\n  `&'a str` implements `sqlx::Decode<'a, sqlx::Any>`\n  `&'r JsonRawValue` implements `sqlx::Decode<'r, DB>`\n  `&'r [u8]` implements `sqlx::Decode<'r, Sqlite>`\n  `&'r [u8]` implements `sqlx::Decode<'r, sqlx::Any>`\n  `&'r str` implements `sqlx::Decode<'r, Sqlite>`\n  `Box<[u8]>` implements `sqlx::Decode<'_, Sqlite>`\n  `Box<str>` implements `sqlx::Decode<'_, Sqlite>`\n  `Cow<'r, str>` implements `sqlx::Decode<'r, Sqlite>`\nand 46 others","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"required by a bound in `try_get_unchecked`","code":null,"level":"note","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-core-0.8.5\\src\\row.rs","byte_start":5166,"byte_end":5183,"line_start":152,"line_end":152,"column_start":8,"column_end":25,"is_primary":false,"text":[{"text":"    fn try_get_unchecked<'r, T, I>(&'r self, index: I) -> Result<T, Error>","highlight_start":8,"highlight_end":25}],"label":"required by a bound in this associated function","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-core-0.8.5\\src\\row.rs","byte_start":5285,"byte_end":5311,"line_start":155,"line_end":155,"column_start":12,"column_end":38,"is_primary":true,"text":[{"text":"        T: Decode<'r, Self::Database>,","highlight_start":12,"highlight_end":38}],"label":"required by this bound in `Row::try_get_unchecked`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0277]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: the trait bound `(): sqlx::Decode<'_, Sqlite>` is not satisfied\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\db\\repositories\\sync_repository.rs:298:31\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m298\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m            let permissions = sqlx::query_as!(\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m _______________________________^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m299\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                UserPermission,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m300\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                r#\"\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m301\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                SELECT id, user_id, resource_type, resource_id, permission, granted_via, granted_via_id, created_at, updated_at \u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m304\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                user_id\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m305\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            )\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|_____________^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mthe trait `sqlx::Decode<'_, Sqlite>` is not implemented for `()`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: the following other types implement trait `sqlx::Decode<'r, DB>`:\u001b[0m\n\u001b[0m              `&'a str` implements `sqlx::Decode<'a, sqlx::Any>`\u001b[0m\n\u001b[0m              `&'r JsonRawValue` implements `sqlx::Decode<'r, DB>`\u001b[0m\n\u001b[0m              `&'r [u8]` implements `sqlx::Decode<'r, Sqlite>`\u001b[0m\n\u001b[0m              `&'r [u8]` implements `sqlx::Decode<'r, sqlx::Any>`\u001b[0m\n\u001b[0m              `&'r str` implements `sqlx::Decode<'r, Sqlite>`\u001b[0m\n\u001b[0m              `Box<[u8]>` implements `sqlx::Decode<'_, Sqlite>`\u001b[0m\n\u001b[0m              `Box<str>` implements `sqlx::Decode<'_, Sqlite>`\u001b[0m\n\u001b[0m              `Cow<'r, str>` implements `sqlx::Decode<'r, Sqlite>`\u001b[0m\n\u001b[0m            and 46 others\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: required by a bound in `try_get_unchecked`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-core-0.8.5\\src\\row.rs:155:12\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m152\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn try_get_unchecked<'r, T, I>(&'r self, index: I) -> Result<T, Error>\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-----------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mrequired by a bound in this associated function\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m155\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        T: Decode<'r, Self::Database>,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10mrequired by this bound in `Row::try_get_unchecked`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query_as` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"the trait bound `uuid::Uuid: From<()>` is not satisfied","code":{"code":"E0277","explanation":"You tried to use a type which doesn't implement some trait in a place which\nexpected that trait.\n\nErroneous code example:\n\n```compile_fail,E0277\n// here we declare the Foo trait with a bar method\ntrait Foo {\n    fn bar(&self);\n}\n\n// we now declare a function which takes an object implementing the Foo trait\nfn some_func<T: Foo>(foo: T) {\n    foo.bar();\n}\n\nfn main() {\n    // we now call the method with the i32 type, which doesn't implement\n    // the Foo trait\n    some_func(5i32); // error: the trait bound `i32 : Foo` is not satisfied\n}\n```\n\nIn order to fix this error, verify that the type you're using does implement\nthe trait. Example:\n\n```\ntrait Foo {\n    fn bar(&self);\n}\n\n// we implement the trait on the i32 type\nimpl Foo for i32 {\n    fn bar(&self) {}\n}\n\nfn some_func<T: Foo>(foo: T) {\n    foo.bar(); // we can now use this method since i32 implements the\n               // Foo trait\n}\n\nfn main() {\n    some_func(5i32); // ok!\n}\n```\n\nOr in a generic context, an erroneous code example would look like:\n\n```compile_fail,E0277\nfn some_func<T>(foo: T) {\n    println!(\"{:?}\", foo); // error: the trait `core::fmt::Debug` is not\n                           //        implemented for the type `T`\n}\n\nfn main() {\n    // We now call the method with the i32 type,\n    // which *does* implement the Debug trait.\n    some_func(5i32);\n}\n```\n\nNote that the error here is in the definition of the generic function. Although\nwe only call it with a parameter that does implement `Debug`, the compiler\nstill rejects the function. It must work with all possible input types. In\norder to make this example compile, we need to restrict the generic type we're\naccepting:\n\n```\nuse std::fmt;\n\n// Restrict the input type to types that implement Debug.\nfn some_func<T: fmt::Debug>(foo: T) {\n    println!(\"{:?}\", foo);\n}\n\nfn main() {\n    // Calling the method is still fine, as i32 implements Debug.\n    some_func(5i32);\n\n    // This would fail to compile now:\n    // struct WithoutDebug;\n    // some_func(WithoutDebug);\n}\n```\n\nRust only looks at the signature of the called function, as such it must\nalready specify all requirements that will be used for every type parameter.\n"},"level":"error","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.8.5\\src\\macros\\mod.rs","byte_start":26699,"byte_end":26792,"line_start":576,"line_end":576,"column_start":9,"column_end":102,"is_primary":true,"text":[{"text":"        $crate::sqlx_macros::expand_query!(record = $out_struct, source = $query, args = [$($args)*])","highlight_start":9,"highlight_end":102}],"label":"the trait `From<()>` is not implemented for `uuid::Uuid`","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.8.5\\src\\macros\\mod.rs","byte_start":26699,"byte_end":26792,"line_start":576,"line_end":576,"column_start":9,"column_end":102,"is_primary":false,"text":[{"text":"        $crate::sqlx_macros::expand_query!(record = $out_struct, source = $query, args = [$($args)*])","highlight_start":9,"highlight_end":102}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\db\\repositories\\sync_repository.rs","byte_start":10844,"byte_end":11156,"line_start":298,"line_end":305,"column_start":31,"column_end":14,"is_primary":false,"text":[{"text":"            let permissions = sqlx::query_as!(","highlight_start":31,"highlight_end":47},{"text":"                UserPermission,","highlight_start":1,"highlight_end":32},{"text":"                r#\"","highlight_start":1,"highlight_end":20},{"text":"                SELECT id, user_id, resource_type, resource_id, permission, granted_via, granted_via_id, created_at, updated_at ","highlight_start":1,"highlight_end":129},{"text":"                FROM user_permissions WHERE user_id = $1","highlight_start":1,"highlight_end":57},{"text":"                \"#,","highlight_start":1,"highlight_end":20},{"text":"                user_id","highlight_start":1,"highlight_end":24},{"text":"            )","highlight_start":1,"highlight_end":14}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"sqlx::query_as!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.8.5\\src\\macros\\mod.rs","byte_start":26477,"byte_end":26498,"line_start":571,"line_end":571,"column_start":1,"column_end":22,"is_primary":false,"text":[{"text":"macro_rules! query_as (","highlight_start":1,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::sqlx_macros::expand_query!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-macros-0.8.5\\src\\lib.rs","byte_start":116,"byte_end":170,"line_start":9,"line_end":9,"column_start":1,"column_end":55,"is_primary":false,"text":[{"text":"pub fn expand_query(input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":55}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[{"message":"the following other types implement trait `From<T>`:\n  `uuid::Uuid` implements `From<Braced>`\n  `uuid::Uuid` implements `From<Hyphenated>`\n  `uuid::Uuid` implements `From<NonNilUuid>`\n  `uuid::Uuid` implements `From<Urn>`\n  `uuid::Uuid` implements `From<uuid::fmt::Simple>`","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"required for `()` to implement `Into<uuid::Uuid>`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0277]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: the trait bound `uuid::Uuid: From<()>` is not satisfied\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\db\\repositories\\sync_repository.rs:298:31\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m298\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m            let permissions = sqlx::query_as!(\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m _______________________________^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m299\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                UserPermission,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m300\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                r#\"\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m301\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                SELECT id, user_id, resource_type, resource_id, permission, granted_via, granted_via_id, created_at, updated_at \u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m304\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                user_id\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m305\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            )\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|_____________^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mthe trait `From<()>` is not implemented for `uuid::Uuid`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: the following other types implement trait `From<T>`:\u001b[0m\n\u001b[0m              `uuid::Uuid` implements `From<Braced>`\u001b[0m\n\u001b[0m              `uuid::Uuid` implements `From<Hyphenated>`\u001b[0m\n\u001b[0m              `uuid::Uuid` implements `From<NonNilUuid>`\u001b[0m\n\u001b[0m              `uuid::Uuid` implements `From<Urn>`\u001b[0m\n\u001b[0m              `uuid::Uuid` implements `From<uuid::fmt::Simple>`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: required for `()` to implement `Into<uuid::Uuid>`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query_as` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"the trait bound `chrono::DateTime<Utc>: From<NaiveDateTime>` is not satisfied","code":{"code":"E0277","explanation":"You tried to use a type which doesn't implement some trait in a place which\nexpected that trait.\n\nErroneous code example:\n\n```compile_fail,E0277\n// here we declare the Foo trait with a bar method\ntrait Foo {\n    fn bar(&self);\n}\n\n// we now declare a function which takes an object implementing the Foo trait\nfn some_func<T: Foo>(foo: T) {\n    foo.bar();\n}\n\nfn main() {\n    // we now call the method with the i32 type, which doesn't implement\n    // the Foo trait\n    some_func(5i32); // error: the trait bound `i32 : Foo` is not satisfied\n}\n```\n\nIn order to fix this error, verify that the type you're using does implement\nthe trait. Example:\n\n```\ntrait Foo {\n    fn bar(&self);\n}\n\n// we implement the trait on the i32 type\nimpl Foo for i32 {\n    fn bar(&self) {}\n}\n\nfn some_func<T: Foo>(foo: T) {\n    foo.bar(); // we can now use this method since i32 implements the\n               // Foo trait\n}\n\nfn main() {\n    some_func(5i32); // ok!\n}\n```\n\nOr in a generic context, an erroneous code example would look like:\n\n```compile_fail,E0277\nfn some_func<T>(foo: T) {\n    println!(\"{:?}\", foo); // error: the trait `core::fmt::Debug` is not\n                           //        implemented for the type `T`\n}\n\nfn main() {\n    // We now call the method with the i32 type,\n    // which *does* implement the Debug trait.\n    some_func(5i32);\n}\n```\n\nNote that the error here is in the definition of the generic function. Although\nwe only call it with a parameter that does implement `Debug`, the compiler\nstill rejects the function. It must work with all possible input types. In\norder to make this example compile, we need to restrict the generic type we're\naccepting:\n\n```\nuse std::fmt;\n\n// Restrict the input type to types that implement Debug.\nfn some_func<T: fmt::Debug>(foo: T) {\n    println!(\"{:?}\", foo);\n}\n\nfn main() {\n    // Calling the method is still fine, as i32 implements Debug.\n    some_func(5i32);\n\n    // This would fail to compile now:\n    // struct WithoutDebug;\n    // some_func(WithoutDebug);\n}\n```\n\nRust only looks at the signature of the called function, as such it must\nalready specify all requirements that will be used for every type parameter.\n"},"level":"error","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.8.5\\src\\macros\\mod.rs","byte_start":26699,"byte_end":26792,"line_start":576,"line_end":576,"column_start":9,"column_end":102,"is_primary":true,"text":[{"text":"        $crate::sqlx_macros::expand_query!(record = $out_struct, source = $query, args = [$($args)*])","highlight_start":9,"highlight_end":102}],"label":"the trait `From<NaiveDateTime>` is not implemented for `chrono::DateTime<Utc>`","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.8.5\\src\\macros\\mod.rs","byte_start":26699,"byte_end":26792,"line_start":576,"line_end":576,"column_start":9,"column_end":102,"is_primary":false,"text":[{"text":"        $crate::sqlx_macros::expand_query!(record = $out_struct, source = $query, args = [$($args)*])","highlight_start":9,"highlight_end":102}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\db\\repositories\\sync_repository.rs","byte_start":10844,"byte_end":11156,"line_start":298,"line_end":305,"column_start":31,"column_end":14,"is_primary":false,"text":[{"text":"            let permissions = sqlx::query_as!(","highlight_start":31,"highlight_end":47},{"text":"                UserPermission,","highlight_start":1,"highlight_end":32},{"text":"                r#\"","highlight_start":1,"highlight_end":20},{"text":"                SELECT id, user_id, resource_type, resource_id, permission, granted_via, granted_via_id, created_at, updated_at ","highlight_start":1,"highlight_end":129},{"text":"                FROM user_permissions WHERE user_id = $1","highlight_start":1,"highlight_end":57},{"text":"                \"#,","highlight_start":1,"highlight_end":20},{"text":"                user_id","highlight_start":1,"highlight_end":24},{"text":"            )","highlight_start":1,"highlight_end":14}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"sqlx::query_as!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.8.5\\src\\macros\\mod.rs","byte_start":26477,"byte_end":26498,"line_start":571,"line_end":571,"column_start":1,"column_end":22,"is_primary":false,"text":[{"text":"macro_rules! query_as (","highlight_start":1,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::sqlx_macros::expand_query!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-macros-0.8.5\\src\\lib.rs","byte_start":116,"byte_end":170,"line_start":9,"line_end":9,"column_start":1,"column_end":55,"is_primary":false,"text":[{"text":"pub fn expand_query(input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":55}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[{"message":"the following other types implement trait `From<T>`:\n  `chrono::DateTime<Utc>` implements `From<chrono::DateTime<FixedOffset>>`\n  `chrono::DateTime<Utc>` implements `From<chrono::DateTime<Local>>`\n  `chrono::DateTime<Utc>` implements `From<std::time::SystemTime>`","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"required for `NaiveDateTime` to implement `Into<chrono::DateTime<Utc>>`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0277]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: the trait bound `chrono::DateTime<Utc>: From<NaiveDateTime>` is not satisfied\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\db\\repositories\\sync_repository.rs:298:31\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m298\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m            let permissions = sqlx::query_as!(\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m _______________________________^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m299\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                UserPermission,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m300\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                r#\"\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m301\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                SELECT id, user_id, resource_type, resource_id, permission, granted_via, granted_via_id, created_at, updated_at \u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m304\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                user_id\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m305\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            )\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|_____________^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mthe trait `From<NaiveDateTime>` is not implemented for `chrono::DateTime<Utc>`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: the following other types implement trait `From<T>`:\u001b[0m\n\u001b[0m              `chrono::DateTime<Utc>` implements `From<chrono::DateTime<FixedOffset>>`\u001b[0m\n\u001b[0m              `chrono::DateTime<Utc>` implements `From<chrono::DateTime<Local>>`\u001b[0m\n\u001b[0m              `chrono::DateTime<Utc>` implements `From<std::time::SystemTime>`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: required for `NaiveDateTime` to implement `Into<chrono::DateTime<Utc>>`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query_as` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no field `pool` on type `connection::Database`","code":{"code":"E0609","explanation":"Attempted to access a nonexistent field in a struct.\n\nErroneous code example:\n\n```compile_fail,E0609\nstruct StructWithFields {\n    x: u32,\n}\n\nlet s = StructWithFields { x: 0 };\nprintln!(\"{}\", s.foo); // error: no field `foo` on type `StructWithFields`\n```\n\nTo fix this error, check that you didn't misspell the field's name or that the\nfield actually exists. Example:\n\n```\nstruct StructWithFields {\n    x: u32,\n}\n\nlet s = StructWithFields { x: 0 };\nprintln!(\"{}\", s.x); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\db\\repositories\\sync_repository.rs","byte_start":11189,"byte_end":11193,"line_start":306,"line_end":306,"column_start":33,"column_end":37,"is_primary":true,"text":[{"text":"            .fetch_all(&self.db.pool)","highlight_start":33,"highlight_end":37}],"label":"unknown field","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"available field is: `connection`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0609]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: no field `pool` on type `connection::Database`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\db\\repositories\\sync_repository.rs:306:33\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m306\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            .fetch_all(&self.db.pool)\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9munknown field\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: available field is: `connection`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no field `pool` on type `connection::Database`","code":{"code":"E0609","explanation":"Attempted to access a nonexistent field in a struct.\n\nErroneous code example:\n\n```compile_fail,E0609\nstruct StructWithFields {\n    x: u32,\n}\n\nlet s = StructWithFields { x: 0 };\nprintln!(\"{}\", s.foo); // error: no field `foo` on type `StructWithFields`\n```\n\nTo fix this error, check that you didn't misspell the field's name or that the\nfield actually exists. Example:\n\n```\nstruct StructWithFields {\n    x: u32,\n}\n\nlet s = StructWithFields { x: 0 };\nprintln!(\"{}\", s.x); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\db\\repositories\\sync_repository.rs","byte_start":12067,"byte_end":12071,"line_start":336,"line_end":336,"column_start":31,"column_end":35,"is_primary":true,"text":[{"text":"            .execute(&self.db.pool)","highlight_start":31,"highlight_end":35}],"label":"unknown field","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"available field is: `connection`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0609]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: no field `pool` on type `connection::Database`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\db\\repositories\\sync_repository.rs:336:31\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m336\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            .execute(&self.db.pool)\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                               \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9munknown field\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: available field is: `connection`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"the trait bound `(): sqlx::Decode<'_, Sqlite>` is not satisfied","code":{"code":"E0277","explanation":"You tried to use a type which doesn't implement some trait in a place which\nexpected that trait.\n\nErroneous code example:\n\n```compile_fail,E0277\n// here we declare the Foo trait with a bar method\ntrait Foo {\n    fn bar(&self);\n}\n\n// we now declare a function which takes an object implementing the Foo trait\nfn some_func<T: Foo>(foo: T) {\n    foo.bar();\n}\n\nfn main() {\n    // we now call the method with the i32 type, which doesn't implement\n    // the Foo trait\n    some_func(5i32); // error: the trait bound `i32 : Foo` is not satisfied\n}\n```\n\nIn order to fix this error, verify that the type you're using does implement\nthe trait. Example:\n\n```\ntrait Foo {\n    fn bar(&self);\n}\n\n// we implement the trait on the i32 type\nimpl Foo for i32 {\n    fn bar(&self) {}\n}\n\nfn some_func<T: Foo>(foo: T) {\n    foo.bar(); // we can now use this method since i32 implements the\n               // Foo trait\n}\n\nfn main() {\n    some_func(5i32); // ok!\n}\n```\n\nOr in a generic context, an erroneous code example would look like:\n\n```compile_fail,E0277\nfn some_func<T>(foo: T) {\n    println!(\"{:?}\", foo); // error: the trait `core::fmt::Debug` is not\n                           //        implemented for the type `T`\n}\n\nfn main() {\n    // We now call the method with the i32 type,\n    // which *does* implement the Debug trait.\n    some_func(5i32);\n}\n```\n\nNote that the error here is in the definition of the generic function. Although\nwe only call it with a parameter that does implement `Debug`, the compiler\nstill rejects the function. It must work with all possible input types. In\norder to make this example compile, we need to restrict the generic type we're\naccepting:\n\n```\nuse std::fmt;\n\n// Restrict the input type to types that implement Debug.\nfn some_func<T: fmt::Debug>(foo: T) {\n    println!(\"{:?}\", foo);\n}\n\nfn main() {\n    // Calling the method is still fine, as i32 implements Debug.\n    some_func(5i32);\n\n    // This would fail to compile now:\n    // struct WithoutDebug;\n    // some_func(WithoutDebug);\n}\n```\n\nRust only looks at the signature of the called function, as such it must\nalready specify all requirements that will be used for every type parameter.\n"},"level":"error","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.8.5\\src\\macros\\mod.rs","byte_start":26699,"byte_end":26792,"line_start":576,"line_end":576,"column_start":9,"column_end":102,"is_primary":true,"text":[{"text":"        $crate::sqlx_macros::expand_query!(record = $out_struct, source = $query, args = [$($args)*])","highlight_start":9,"highlight_end":102}],"label":"the trait `sqlx::Decode<'_, Sqlite>` is not implemented for `()`","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.8.5\\src\\macros\\mod.rs","byte_start":26699,"byte_end":26792,"line_start":576,"line_end":576,"column_start":9,"column_end":102,"is_primary":false,"text":[{"text":"        $crate::sqlx_macros::expand_query!(record = $out_struct, source = $query, args = [$($args)*])","highlight_start":9,"highlight_end":102}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\db\\repositories\\sync_repository.rs","byte_start":12637,"byte_end":13314,"line_start":359,"line_end":373,"column_start":26,"column_end":14,"is_primary":false,"text":[{"text":"            let result = sqlx::query_as!(","highlight_start":26,"highlight_end":42},{"text":"                AgentPermission,","highlight_start":1,"highlight_end":33},{"text":"                r#\"","highlight_start":1,"highlight_end":20},{"text":"                INSERT INTO agent_permissions (id, agent_id, resource_type, resource_id, permission, granted_via, granted_via_id)","highlight_start":1,"highlight_end":130},{"text":"                VALUES ($1, $2, $3, $4, $5, $6, $7)","highlight_start":1,"highlight_end":52},{"text":"                RETURNING id, agent_id, resource_type, resource_id, permission, granted_via, granted_via_id, created_at, updated_at","highlight_start":1,"highlight_end":132},{"text":"                \"#,","highlight_start":1,"highlight_end":20},{"text":"                id,","highlight_start":1,"highlight_end":20},{"text":"                permission.agent_id,","highlight_start":1,"highlight_end":37},{"text":"                permission.resource_type,","highlight_start":1,"highlight_end":42},{"text":"                permission.resource_id,","highlight_start":1,"highlight_end":40},{"text":"                permission.permission,","highlight_start":1,"highlight_end":39},{"text":"                permission.granted_via,","highlight_start":1,"highlight_end":40},{"text":"                permission.granted_via_id","highlight_start":1,"highlight_end":42},{"text":"            )","highlight_start":1,"highlight_end":14}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"sqlx::query_as!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.8.5\\src\\macros\\mod.rs","byte_start":26477,"byte_end":26498,"line_start":571,"line_end":571,"column_start":1,"column_end":22,"is_primary":false,"text":[{"text":"macro_rules! query_as (","highlight_start":1,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::sqlx_macros::expand_query!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-macros-0.8.5\\src\\lib.rs","byte_start":116,"byte_end":170,"line_start":9,"line_end":9,"column_start":1,"column_end":55,"is_primary":false,"text":[{"text":"pub fn expand_query(input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":55}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[{"message":"the following other types implement trait `sqlx::Decode<'r, DB>`:\n  `&'a str` implements `sqlx::Decode<'a, sqlx::Any>`\n  `&'r JsonRawValue` implements `sqlx::Decode<'r, DB>`\n  `&'r [u8]` implements `sqlx::Decode<'r, Sqlite>`\n  `&'r [u8]` implements `sqlx::Decode<'r, sqlx::Any>`\n  `&'r str` implements `sqlx::Decode<'r, Sqlite>`\n  `Box<[u8]>` implements `sqlx::Decode<'_, Sqlite>`\n  `Box<str>` implements `sqlx::Decode<'_, Sqlite>`\n  `Cow<'r, str>` implements `sqlx::Decode<'r, Sqlite>`\nand 46 others","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"required by a bound in `try_get_unchecked`","code":null,"level":"note","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-core-0.8.5\\src\\row.rs","byte_start":5166,"byte_end":5183,"line_start":152,"line_end":152,"column_start":8,"column_end":25,"is_primary":false,"text":[{"text":"    fn try_get_unchecked<'r, T, I>(&'r self, index: I) -> Result<T, Error>","highlight_start":8,"highlight_end":25}],"label":"required by a bound in this associated function","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-core-0.8.5\\src\\row.rs","byte_start":5285,"byte_end":5311,"line_start":155,"line_end":155,"column_start":12,"column_end":38,"is_primary":true,"text":[{"text":"        T: Decode<'r, Self::Database>,","highlight_start":12,"highlight_end":38}],"label":"required by this bound in `Row::try_get_unchecked`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0277]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: the trait bound `(): sqlx::Decode<'_, Sqlite>` is not satisfied\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\db\\repositories\\sync_repository.rs:359:26\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m359\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m            let result = sqlx::query_as!(\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m __________________________^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m360\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                AgentPermission,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m361\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                r#\"\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m362\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                INSERT INTO agent_permissions (id, agent_id, resource_type, resource_id, permission, granted_via, granted_via_id)\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m372\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                permission.granted_via_id\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m373\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            )\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|_____________^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mthe trait `sqlx::Decode<'_, Sqlite>` is not implemented for `()`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: the following other types implement trait `sqlx::Decode<'r, DB>`:\u001b[0m\n\u001b[0m              `&'a str` implements `sqlx::Decode<'a, sqlx::Any>`\u001b[0m\n\u001b[0m              `&'r JsonRawValue` implements `sqlx::Decode<'r, DB>`\u001b[0m\n\u001b[0m              `&'r [u8]` implements `sqlx::Decode<'r, Sqlite>`\u001b[0m\n\u001b[0m              `&'r [u8]` implements `sqlx::Decode<'r, sqlx::Any>`\u001b[0m\n\u001b[0m              `&'r str` implements `sqlx::Decode<'r, Sqlite>`\u001b[0m\n\u001b[0m              `Box<[u8]>` implements `sqlx::Decode<'_, Sqlite>`\u001b[0m\n\u001b[0m              `Box<str>` implements `sqlx::Decode<'_, Sqlite>`\u001b[0m\n\u001b[0m              `Cow<'r, str>` implements `sqlx::Decode<'r, Sqlite>`\u001b[0m\n\u001b[0m            and 46 others\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: required by a bound in `try_get_unchecked`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-core-0.8.5\\src\\row.rs:155:12\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m152\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn try_get_unchecked<'r, T, I>(&'r self, index: I) -> Result<T, Error>\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-----------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mrequired by a bound in this associated function\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m155\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        T: Decode<'r, Self::Database>,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10mrequired by this bound in `Row::try_get_unchecked`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query_as` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"the trait bound `uuid::Uuid: From<()>` is not satisfied","code":{"code":"E0277","explanation":"You tried to use a type which doesn't implement some trait in a place which\nexpected that trait.\n\nErroneous code example:\n\n```compile_fail,E0277\n// here we declare the Foo trait with a bar method\ntrait Foo {\n    fn bar(&self);\n}\n\n// we now declare a function which takes an object implementing the Foo trait\nfn some_func<T: Foo>(foo: T) {\n    foo.bar();\n}\n\nfn main() {\n    // we now call the method with the i32 type, which doesn't implement\n    // the Foo trait\n    some_func(5i32); // error: the trait bound `i32 : Foo` is not satisfied\n}\n```\n\nIn order to fix this error, verify that the type you're using does implement\nthe trait. Example:\n\n```\ntrait Foo {\n    fn bar(&self);\n}\n\n// we implement the trait on the i32 type\nimpl Foo for i32 {\n    fn bar(&self) {}\n}\n\nfn some_func<T: Foo>(foo: T) {\n    foo.bar(); // we can now use this method since i32 implements the\n               // Foo trait\n}\n\nfn main() {\n    some_func(5i32); // ok!\n}\n```\n\nOr in a generic context, an erroneous code example would look like:\n\n```compile_fail,E0277\nfn some_func<T>(foo: T) {\n    println!(\"{:?}\", foo); // error: the trait `core::fmt::Debug` is not\n                           //        implemented for the type `T`\n}\n\nfn main() {\n    // We now call the method with the i32 type,\n    // which *does* implement the Debug trait.\n    some_func(5i32);\n}\n```\n\nNote that the error here is in the definition of the generic function. Although\nwe only call it with a parameter that does implement `Debug`, the compiler\nstill rejects the function. It must work with all possible input types. In\norder to make this example compile, we need to restrict the generic type we're\naccepting:\n\n```\nuse std::fmt;\n\n// Restrict the input type to types that implement Debug.\nfn some_func<T: fmt::Debug>(foo: T) {\n    println!(\"{:?}\", foo);\n}\n\nfn main() {\n    // Calling the method is still fine, as i32 implements Debug.\n    some_func(5i32);\n\n    // This would fail to compile now:\n    // struct WithoutDebug;\n    // some_func(WithoutDebug);\n}\n```\n\nRust only looks at the signature of the called function, as such it must\nalready specify all requirements that will be used for every type parameter.\n"},"level":"error","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.8.5\\src\\macros\\mod.rs","byte_start":26699,"byte_end":26792,"line_start":576,"line_end":576,"column_start":9,"column_end":102,"is_primary":true,"text":[{"text":"        $crate::sqlx_macros::expand_query!(record = $out_struct, source = $query, args = [$($args)*])","highlight_start":9,"highlight_end":102}],"label":"the trait `From<()>` is not implemented for `uuid::Uuid`","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.8.5\\src\\macros\\mod.rs","byte_start":26699,"byte_end":26792,"line_start":576,"line_end":576,"column_start":9,"column_end":102,"is_primary":false,"text":[{"text":"        $crate::sqlx_macros::expand_query!(record = $out_struct, source = $query, args = [$($args)*])","highlight_start":9,"highlight_end":102}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\db\\repositories\\sync_repository.rs","byte_start":12637,"byte_end":13314,"line_start":359,"line_end":373,"column_start":26,"column_end":14,"is_primary":false,"text":[{"text":"            let result = sqlx::query_as!(","highlight_start":26,"highlight_end":42},{"text":"                AgentPermission,","highlight_start":1,"highlight_end":33},{"text":"                r#\"","highlight_start":1,"highlight_end":20},{"text":"                INSERT INTO agent_permissions (id, agent_id, resource_type, resource_id, permission, granted_via, granted_via_id)","highlight_start":1,"highlight_end":130},{"text":"                VALUES ($1, $2, $3, $4, $5, $6, $7)","highlight_start":1,"highlight_end":52},{"text":"                RETURNING id, agent_id, resource_type, resource_id, permission, granted_via, granted_via_id, created_at, updated_at","highlight_start":1,"highlight_end":132},{"text":"                \"#,","highlight_start":1,"highlight_end":20},{"text":"                id,","highlight_start":1,"highlight_end":20},{"text":"                permission.agent_id,","highlight_start":1,"highlight_end":37},{"text":"                permission.resource_type,","highlight_start":1,"highlight_end":42},{"text":"                permission.resource_id,","highlight_start":1,"highlight_end":40},{"text":"                permission.permission,","highlight_start":1,"highlight_end":39},{"text":"                permission.granted_via,","highlight_start":1,"highlight_end":40},{"text":"                permission.granted_via_id","highlight_start":1,"highlight_end":42},{"text":"            )","highlight_start":1,"highlight_end":14}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"sqlx::query_as!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.8.5\\src\\macros\\mod.rs","byte_start":26477,"byte_end":26498,"line_start":571,"line_end":571,"column_start":1,"column_end":22,"is_primary":false,"text":[{"text":"macro_rules! query_as (","highlight_start":1,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::sqlx_macros::expand_query!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-macros-0.8.5\\src\\lib.rs","byte_start":116,"byte_end":170,"line_start":9,"line_end":9,"column_start":1,"column_end":55,"is_primary":false,"text":[{"text":"pub fn expand_query(input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":55}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[{"message":"the following other types implement trait `From<T>`:\n  `uuid::Uuid` implements `From<Braced>`\n  `uuid::Uuid` implements `From<Hyphenated>`\n  `uuid::Uuid` implements `From<NonNilUuid>`\n  `uuid::Uuid` implements `From<Urn>`\n  `uuid::Uuid` implements `From<uuid::fmt::Simple>`","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"required for `()` to implement `Into<uuid::Uuid>`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0277]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: the trait bound `uuid::Uuid: From<()>` is not satisfied\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\db\\repositories\\sync_repository.rs:359:26\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m359\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m            let result = sqlx::query_as!(\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m __________________________^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m360\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                AgentPermission,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m361\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                r#\"\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m362\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                INSERT INTO agent_permissions (id, agent_id, resource_type, resource_id, permission, granted_via, granted_via_id)\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m372\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                permission.granted_via_id\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m373\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            )\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|_____________^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mthe trait `From<()>` is not implemented for `uuid::Uuid`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: the following other types implement trait `From<T>`:\u001b[0m\n\u001b[0m              `uuid::Uuid` implements `From<Braced>`\u001b[0m\n\u001b[0m              `uuid::Uuid` implements `From<Hyphenated>`\u001b[0m\n\u001b[0m              `uuid::Uuid` implements `From<NonNilUuid>`\u001b[0m\n\u001b[0m              `uuid::Uuid` implements `From<Urn>`\u001b[0m\n\u001b[0m              `uuid::Uuid` implements `From<uuid::fmt::Simple>`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: required for `()` to implement `Into<uuid::Uuid>`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query_as` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"the trait bound `chrono::DateTime<Utc>: From<NaiveDateTime>` is not satisfied","code":{"code":"E0277","explanation":"You tried to use a type which doesn't implement some trait in a place which\nexpected that trait.\n\nErroneous code example:\n\n```compile_fail,E0277\n// here we declare the Foo trait with a bar method\ntrait Foo {\n    fn bar(&self);\n}\n\n// we now declare a function which takes an object implementing the Foo trait\nfn some_func<T: Foo>(foo: T) {\n    foo.bar();\n}\n\nfn main() {\n    // we now call the method with the i32 type, which doesn't implement\n    // the Foo trait\n    some_func(5i32); // error: the trait bound `i32 : Foo` is not satisfied\n}\n```\n\nIn order to fix this error, verify that the type you're using does implement\nthe trait. Example:\n\n```\ntrait Foo {\n    fn bar(&self);\n}\n\n// we implement the trait on the i32 type\nimpl Foo for i32 {\n    fn bar(&self) {}\n}\n\nfn some_func<T: Foo>(foo: T) {\n    foo.bar(); // we can now use this method since i32 implements the\n               // Foo trait\n}\n\nfn main() {\n    some_func(5i32); // ok!\n}\n```\n\nOr in a generic context, an erroneous code example would look like:\n\n```compile_fail,E0277\nfn some_func<T>(foo: T) {\n    println!(\"{:?}\", foo); // error: the trait `core::fmt::Debug` is not\n                           //        implemented for the type `T`\n}\n\nfn main() {\n    // We now call the method with the i32 type,\n    // which *does* implement the Debug trait.\n    some_func(5i32);\n}\n```\n\nNote that the error here is in the definition of the generic function. Although\nwe only call it with a parameter that does implement `Debug`, the compiler\nstill rejects the function. It must work with all possible input types. In\norder to make this example compile, we need to restrict the generic type we're\naccepting:\n\n```\nuse std::fmt;\n\n// Restrict the input type to types that implement Debug.\nfn some_func<T: fmt::Debug>(foo: T) {\n    println!(\"{:?}\", foo);\n}\n\nfn main() {\n    // Calling the method is still fine, as i32 implements Debug.\n    some_func(5i32);\n\n    // This would fail to compile now:\n    // struct WithoutDebug;\n    // some_func(WithoutDebug);\n}\n```\n\nRust only looks at the signature of the called function, as such it must\nalready specify all requirements that will be used for every type parameter.\n"},"level":"error","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.8.5\\src\\macros\\mod.rs","byte_start":26699,"byte_end":26792,"line_start":576,"line_end":576,"column_start":9,"column_end":102,"is_primary":true,"text":[{"text":"        $crate::sqlx_macros::expand_query!(record = $out_struct, source = $query, args = [$($args)*])","highlight_start":9,"highlight_end":102}],"label":"the trait `From<NaiveDateTime>` is not implemented for `chrono::DateTime<Utc>`","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.8.5\\src\\macros\\mod.rs","byte_start":26699,"byte_end":26792,"line_start":576,"line_end":576,"column_start":9,"column_end":102,"is_primary":false,"text":[{"text":"        $crate::sqlx_macros::expand_query!(record = $out_struct, source = $query, args = [$($args)*])","highlight_start":9,"highlight_end":102}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\db\\repositories\\sync_repository.rs","byte_start":12637,"byte_end":13314,"line_start":359,"line_end":373,"column_start":26,"column_end":14,"is_primary":false,"text":[{"text":"            let result = sqlx::query_as!(","highlight_start":26,"highlight_end":42},{"text":"                AgentPermission,","highlight_start":1,"highlight_end":33},{"text":"                r#\"","highlight_start":1,"highlight_end":20},{"text":"                INSERT INTO agent_permissions (id, agent_id, resource_type, resource_id, permission, granted_via, granted_via_id)","highlight_start":1,"highlight_end":130},{"text":"                VALUES ($1, $2, $3, $4, $5, $6, $7)","highlight_start":1,"highlight_end":52},{"text":"                RETURNING id, agent_id, resource_type, resource_id, permission, granted_via, granted_via_id, created_at, updated_at","highlight_start":1,"highlight_end":132},{"text":"                \"#,","highlight_start":1,"highlight_end":20},{"text":"                id,","highlight_start":1,"highlight_end":20},{"text":"                permission.agent_id,","highlight_start":1,"highlight_end":37},{"text":"                permission.resource_type,","highlight_start":1,"highlight_end":42},{"text":"                permission.resource_id,","highlight_start":1,"highlight_end":40},{"text":"                permission.permission,","highlight_start":1,"highlight_end":39},{"text":"                permission.granted_via,","highlight_start":1,"highlight_end":40},{"text":"                permission.granted_via_id","highlight_start":1,"highlight_end":42},{"text":"            )","highlight_start":1,"highlight_end":14}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"sqlx::query_as!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.8.5\\src\\macros\\mod.rs","byte_start":26477,"byte_end":26498,"line_start":571,"line_end":571,"column_start":1,"column_end":22,"is_primary":false,"text":[{"text":"macro_rules! query_as (","highlight_start":1,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::sqlx_macros::expand_query!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-macros-0.8.5\\src\\lib.rs","byte_start":116,"byte_end":170,"line_start":9,"line_end":9,"column_start":1,"column_end":55,"is_primary":false,"text":[{"text":"pub fn expand_query(input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":55}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[{"message":"the following other types implement trait `From<T>`:\n  `chrono::DateTime<Utc>` implements `From<chrono::DateTime<FixedOffset>>`\n  `chrono::DateTime<Utc>` implements `From<chrono::DateTime<Local>>`\n  `chrono::DateTime<Utc>` implements `From<std::time::SystemTime>`","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"required for `NaiveDateTime` to implement `Into<chrono::DateTime<Utc>>`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0277]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: the trait bound `chrono::DateTime<Utc>: From<NaiveDateTime>` is not satisfied\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\db\\repositories\\sync_repository.rs:359:26\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m359\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m            let result = sqlx::query_as!(\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m __________________________^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m360\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                AgentPermission,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m361\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                r#\"\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m362\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                INSERT INTO agent_permissions (id, agent_id, resource_type, resource_id, permission, granted_via, granted_via_id)\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m372\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                permission.granted_via_id\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m373\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            )\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|_____________^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mthe trait `From<NaiveDateTime>` is not implemented for `chrono::DateTime<Utc>`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: the following other types implement trait `From<T>`:\u001b[0m\n\u001b[0m              `chrono::DateTime<Utc>` implements `From<chrono::DateTime<FixedOffset>>`\u001b[0m\n\u001b[0m              `chrono::DateTime<Utc>` implements `From<chrono::DateTime<Local>>`\u001b[0m\n\u001b[0m              `chrono::DateTime<Utc>` implements `From<std::time::SystemTime>`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: required for `NaiveDateTime` to implement `Into<chrono::DateTime<Utc>>`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query_as` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no field `pool` on type `connection::Database`","code":{"code":"E0609","explanation":"Attempted to access a nonexistent field in a struct.\n\nErroneous code example:\n\n```compile_fail,E0609\nstruct StructWithFields {\n    x: u32,\n}\n\nlet s = StructWithFields { x: 0 };\nprintln!(\"{}\", s.foo); // error: no field `foo` on type `StructWithFields`\n```\n\nTo fix this error, check that you didn't misspell the field's name or that the\nfield actually exists. Example:\n\n```\nstruct StructWithFields {\n    x: u32,\n}\n\nlet s = StructWithFields { x: 0 };\nprintln!(\"{}\", s.x); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\db\\repositories\\sync_repository.rs","byte_start":13347,"byte_end":13351,"line_start":374,"line_end":374,"column_start":33,"column_end":37,"is_primary":true,"text":[{"text":"            .fetch_one(&self.db.pool)","highlight_start":33,"highlight_end":37}],"label":"unknown field","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"available field is: `connection`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0609]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: no field `pool` on type `connection::Database`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\db\\repositories\\sync_repository.rs:374:33\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m374\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            .fetch_one(&self.db.pool)\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9munknown field\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: available field is: `connection`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no field `pool` on type `connection::Database`","code":{"code":"E0609","explanation":"Attempted to access a nonexistent field in a struct.\n\nErroneous code example:\n\n```compile_fail,E0609\nstruct StructWithFields {\n    x: u32,\n}\n\nlet s = StructWithFields { x: 0 };\nprintln!(\"{}\", s.foo); // error: no field `foo` on type `StructWithFields`\n```\n\nTo fix this error, check that you didn't misspell the field's name or that the\nfield actually exists. Example:\n\n```\nstruct StructWithFields {\n    x: u32,\n}\n\nlet s = StructWithFields { x: 0 };\nprintln!(\"{}\", s.x); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\db\\repositories\\sync_repository.rs","byte_start":15121,"byte_end":15125,"line_start":429,"line_end":429,"column_start":31,"column_end":35,"is_primary":true,"text":[{"text":"            .execute(&self.db.pool)","highlight_start":31,"highlight_end":35}],"label":"unknown field","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"available field is: `connection`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0609]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: no field `pool` on type `connection::Database`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\db\\repositories\\sync_repository.rs:429:31\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m429\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            .execute(&self.db.pool)\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                               \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9munknown field\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: available field is: `connection`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"the trait bound `(): sqlx::Decode<'_, Sqlite>` is not satisfied","code":{"code":"E0277","explanation":"You tried to use a type which doesn't implement some trait in a place which\nexpected that trait.\n\nErroneous code example:\n\n```compile_fail,E0277\n// here we declare the Foo trait with a bar method\ntrait Foo {\n    fn bar(&self);\n}\n\n// we now declare a function which takes an object implementing the Foo trait\nfn some_func<T: Foo>(foo: T) {\n    foo.bar();\n}\n\nfn main() {\n    // we now call the method with the i32 type, which doesn't implement\n    // the Foo trait\n    some_func(5i32); // error: the trait bound `i32 : Foo` is not satisfied\n}\n```\n\nIn order to fix this error, verify that the type you're using does implement\nthe trait. Example:\n\n```\ntrait Foo {\n    fn bar(&self);\n}\n\n// we implement the trait on the i32 type\nimpl Foo for i32 {\n    fn bar(&self) {}\n}\n\nfn some_func<T: Foo>(foo: T) {\n    foo.bar(); // we can now use this method since i32 implements the\n               // Foo trait\n}\n\nfn main() {\n    some_func(5i32); // ok!\n}\n```\n\nOr in a generic context, an erroneous code example would look like:\n\n```compile_fail,E0277\nfn some_func<T>(foo: T) {\n    println!(\"{:?}\", foo); // error: the trait `core::fmt::Debug` is not\n                           //        implemented for the type `T`\n}\n\nfn main() {\n    // We now call the method with the i32 type,\n    // which *does* implement the Debug trait.\n    some_func(5i32);\n}\n```\n\nNote that the error here is in the definition of the generic function. Although\nwe only call it with a parameter that does implement `Debug`, the compiler\nstill rejects the function. It must work with all possible input types. In\norder to make this example compile, we need to restrict the generic type we're\naccepting:\n\n```\nuse std::fmt;\n\n// Restrict the input type to types that implement Debug.\nfn some_func<T: fmt::Debug>(foo: T) {\n    println!(\"{:?}\", foo);\n}\n\nfn main() {\n    // Calling the method is still fine, as i32 implements Debug.\n    some_func(5i32);\n\n    // This would fail to compile now:\n    // struct WithoutDebug;\n    // some_func(WithoutDebug);\n}\n```\n\nRust only looks at the signature of the called function, as such it must\nalready specify all requirements that will be used for every type parameter.\n"},"level":"error","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.8.5\\src\\macros\\mod.rs","byte_start":26699,"byte_end":26792,"line_start":576,"line_end":576,"column_start":9,"column_end":102,"is_primary":true,"text":[{"text":"        $crate::sqlx_macros::expand_query!(record = $out_struct, source = $query, args = [$($args)*])","highlight_start":9,"highlight_end":102}],"label":"the trait `sqlx::Decode<'_, Sqlite>` is not implemented for `()`","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.8.5\\src\\macros\\mod.rs","byte_start":26699,"byte_end":26792,"line_start":576,"line_end":576,"column_start":9,"column_end":102,"is_primary":false,"text":[{"text":"        $crate::sqlx_macros::expand_query!(record = $out_struct, source = $query, args = [$($args)*])","highlight_start":9,"highlight_end":102}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\db\\repositories\\sync_repository.rs","byte_start":16074,"byte_end":16390,"line_start":461,"line_end":468,"column_start":31,"column_end":14,"is_primary":false,"text":[{"text":"            let permissions = sqlx::query_as!(","highlight_start":31,"highlight_end":47},{"text":"                AgentPermission,","highlight_start":1,"highlight_end":33},{"text":"                r#\"","highlight_start":1,"highlight_end":20},{"text":"                SELECT id, agent_id, resource_type, resource_id, permission, granted_via, granted_via_id, created_at, updated_at","highlight_start":1,"highlight_end":129},{"text":"                FROM agent_permissions WHERE agent_id = $1","highlight_start":1,"highlight_end":59},{"text":"                \"#,","highlight_start":1,"highlight_end":20},{"text":"                agent_id","highlight_start":1,"highlight_end":25},{"text":"            )","highlight_start":1,"highlight_end":14}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"sqlx::query_as!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.8.5\\src\\macros\\mod.rs","byte_start":26477,"byte_end":26498,"line_start":571,"line_end":571,"column_start":1,"column_end":22,"is_primary":false,"text":[{"text":"macro_rules! query_as (","highlight_start":1,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::sqlx_macros::expand_query!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-macros-0.8.5\\src\\lib.rs","byte_start":116,"byte_end":170,"line_start":9,"line_end":9,"column_start":1,"column_end":55,"is_primary":false,"text":[{"text":"pub fn expand_query(input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":55}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[{"message":"the following other types implement trait `sqlx::Decode<'r, DB>`:\n  `&'a str` implements `sqlx::Decode<'a, sqlx::Any>`\n  `&'r JsonRawValue` implements `sqlx::Decode<'r, DB>`\n  `&'r [u8]` implements `sqlx::Decode<'r, Sqlite>`\n  `&'r [u8]` implements `sqlx::Decode<'r, sqlx::Any>`\n  `&'r str` implements `sqlx::Decode<'r, Sqlite>`\n  `Box<[u8]>` implements `sqlx::Decode<'_, Sqlite>`\n  `Box<str>` implements `sqlx::Decode<'_, Sqlite>`\n  `Cow<'r, str>` implements `sqlx::Decode<'r, Sqlite>`\nand 46 others","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"required by a bound in `try_get_unchecked`","code":null,"level":"note","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-core-0.8.5\\src\\row.rs","byte_start":5166,"byte_end":5183,"line_start":152,"line_end":152,"column_start":8,"column_end":25,"is_primary":false,"text":[{"text":"    fn try_get_unchecked<'r, T, I>(&'r self, index: I) -> Result<T, Error>","highlight_start":8,"highlight_end":25}],"label":"required by a bound in this associated function","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-core-0.8.5\\src\\row.rs","byte_start":5285,"byte_end":5311,"line_start":155,"line_end":155,"column_start":12,"column_end":38,"is_primary":true,"text":[{"text":"        T: Decode<'r, Self::Database>,","highlight_start":12,"highlight_end":38}],"label":"required by this bound in `Row::try_get_unchecked`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0277]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: the trait bound `(): sqlx::Decode<'_, Sqlite>` is not satisfied\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\db\\repositories\\sync_repository.rs:461:31\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m461\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m            let permissions = sqlx::query_as!(\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m _______________________________^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m462\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                AgentPermission,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m463\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                r#\"\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m464\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                SELECT id, agent_id, resource_type, resource_id, permission, granted_via, granted_via_id, created_at, updated_at\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m467\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                agent_id\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m468\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            )\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|_____________^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mthe trait `sqlx::Decode<'_, Sqlite>` is not implemented for `()`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: the following other types implement trait `sqlx::Decode<'r, DB>`:\u001b[0m\n\u001b[0m              `&'a str` implements `sqlx::Decode<'a, sqlx::Any>`\u001b[0m\n\u001b[0m              `&'r JsonRawValue` implements `sqlx::Decode<'r, DB>`\u001b[0m\n\u001b[0m              `&'r [u8]` implements `sqlx::Decode<'r, Sqlite>`\u001b[0m\n\u001b[0m              `&'r [u8]` implements `sqlx::Decode<'r, sqlx::Any>`\u001b[0m\n\u001b[0m              `&'r str` implements `sqlx::Decode<'r, Sqlite>`\u001b[0m\n\u001b[0m              `Box<[u8]>` implements `sqlx::Decode<'_, Sqlite>`\u001b[0m\n\u001b[0m              `Box<str>` implements `sqlx::Decode<'_, Sqlite>`\u001b[0m\n\u001b[0m              `Cow<'r, str>` implements `sqlx::Decode<'r, Sqlite>`\u001b[0m\n\u001b[0m            and 46 others\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: required by a bound in `try_get_unchecked`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-core-0.8.5\\src\\row.rs:155:12\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m152\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn try_get_unchecked<'r, T, I>(&'r self, index: I) -> Result<T, Error>\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-----------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mrequired by a bound in this associated function\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m155\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        T: Decode<'r, Self::Database>,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10mrequired by this bound in `Row::try_get_unchecked`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query_as` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"the trait bound `uuid::Uuid: From<()>` is not satisfied","code":{"code":"E0277","explanation":"You tried to use a type which doesn't implement some trait in a place which\nexpected that trait.\n\nErroneous code example:\n\n```compile_fail,E0277\n// here we declare the Foo trait with a bar method\ntrait Foo {\n    fn bar(&self);\n}\n\n// we now declare a function which takes an object implementing the Foo trait\nfn some_func<T: Foo>(foo: T) {\n    foo.bar();\n}\n\nfn main() {\n    // we now call the method with the i32 type, which doesn't implement\n    // the Foo trait\n    some_func(5i32); // error: the trait bound `i32 : Foo` is not satisfied\n}\n```\n\nIn order to fix this error, verify that the type you're using does implement\nthe trait. Example:\n\n```\ntrait Foo {\n    fn bar(&self);\n}\n\n// we implement the trait on the i32 type\nimpl Foo for i32 {\n    fn bar(&self) {}\n}\n\nfn some_func<T: Foo>(foo: T) {\n    foo.bar(); // we can now use this method since i32 implements the\n               // Foo trait\n}\n\nfn main() {\n    some_func(5i32); // ok!\n}\n```\n\nOr in a generic context, an erroneous code example would look like:\n\n```compile_fail,E0277\nfn some_func<T>(foo: T) {\n    println!(\"{:?}\", foo); // error: the trait `core::fmt::Debug` is not\n                           //        implemented for the type `T`\n}\n\nfn main() {\n    // We now call the method with the i32 type,\n    // which *does* implement the Debug trait.\n    some_func(5i32);\n}\n```\n\nNote that the error here is in the definition of the generic function. Although\nwe only call it with a parameter that does implement `Debug`, the compiler\nstill rejects the function. It must work with all possible input types. In\norder to make this example compile, we need to restrict the generic type we're\naccepting:\n\n```\nuse std::fmt;\n\n// Restrict the input type to types that implement Debug.\nfn some_func<T: fmt::Debug>(foo: T) {\n    println!(\"{:?}\", foo);\n}\n\nfn main() {\n    // Calling the method is still fine, as i32 implements Debug.\n    some_func(5i32);\n\n    // This would fail to compile now:\n    // struct WithoutDebug;\n    // some_func(WithoutDebug);\n}\n```\n\nRust only looks at the signature of the called function, as such it must\nalready specify all requirements that will be used for every type parameter.\n"},"level":"error","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.8.5\\src\\macros\\mod.rs","byte_start":26699,"byte_end":26792,"line_start":576,"line_end":576,"column_start":9,"column_end":102,"is_primary":true,"text":[{"text":"        $crate::sqlx_macros::expand_query!(record = $out_struct, source = $query, args = [$($args)*])","highlight_start":9,"highlight_end":102}],"label":"the trait `From<()>` is not implemented for `uuid::Uuid`","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.8.5\\src\\macros\\mod.rs","byte_start":26699,"byte_end":26792,"line_start":576,"line_end":576,"column_start":9,"column_end":102,"is_primary":false,"text":[{"text":"        $crate::sqlx_macros::expand_query!(record = $out_struct, source = $query, args = [$($args)*])","highlight_start":9,"highlight_end":102}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\db\\repositories\\sync_repository.rs","byte_start":16074,"byte_end":16390,"line_start":461,"line_end":468,"column_start":31,"column_end":14,"is_primary":false,"text":[{"text":"            let permissions = sqlx::query_as!(","highlight_start":31,"highlight_end":47},{"text":"                AgentPermission,","highlight_start":1,"highlight_end":33},{"text":"                r#\"","highlight_start":1,"highlight_end":20},{"text":"                SELECT id, agent_id, resource_type, resource_id, permission, granted_via, granted_via_id, created_at, updated_at","highlight_start":1,"highlight_end":129},{"text":"                FROM agent_permissions WHERE agent_id = $1","highlight_start":1,"highlight_end":59},{"text":"                \"#,","highlight_start":1,"highlight_end":20},{"text":"                agent_id","highlight_start":1,"highlight_end":25},{"text":"            )","highlight_start":1,"highlight_end":14}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"sqlx::query_as!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.8.5\\src\\macros\\mod.rs","byte_start":26477,"byte_end":26498,"line_start":571,"line_end":571,"column_start":1,"column_end":22,"is_primary":false,"text":[{"text":"macro_rules! query_as (","highlight_start":1,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::sqlx_macros::expand_query!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-macros-0.8.5\\src\\lib.rs","byte_start":116,"byte_end":170,"line_start":9,"line_end":9,"column_start":1,"column_end":55,"is_primary":false,"text":[{"text":"pub fn expand_query(input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":55}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[{"message":"the following other types implement trait `From<T>`:\n  `uuid::Uuid` implements `From<Braced>`\n  `uuid::Uuid` implements `From<Hyphenated>`\n  `uuid::Uuid` implements `From<NonNilUuid>`\n  `uuid::Uuid` implements `From<Urn>`\n  `uuid::Uuid` implements `From<uuid::fmt::Simple>`","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"required for `()` to implement `Into<uuid::Uuid>`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0277]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: the trait bound `uuid::Uuid: From<()>` is not satisfied\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\db\\repositories\\sync_repository.rs:461:31\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m461\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m            let permissions = sqlx::query_as!(\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m _______________________________^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m462\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                AgentPermission,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m463\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                r#\"\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m464\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                SELECT id, agent_id, resource_type, resource_id, permission, granted_via, granted_via_id, created_at, updated_at\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m467\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                agent_id\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m468\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            )\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|_____________^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mthe trait `From<()>` is not implemented for `uuid::Uuid`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: the following other types implement trait `From<T>`:\u001b[0m\n\u001b[0m              `uuid::Uuid` implements `From<Braced>`\u001b[0m\n\u001b[0m              `uuid::Uuid` implements `From<Hyphenated>`\u001b[0m\n\u001b[0m              `uuid::Uuid` implements `From<NonNilUuid>`\u001b[0m\n\u001b[0m              `uuid::Uuid` implements `From<Urn>`\u001b[0m\n\u001b[0m              `uuid::Uuid` implements `From<uuid::fmt::Simple>`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: required for `()` to implement `Into<uuid::Uuid>`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query_as` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"the trait bound `chrono::DateTime<Utc>: From<NaiveDateTime>` is not satisfied","code":{"code":"E0277","explanation":"You tried to use a type which doesn't implement some trait in a place which\nexpected that trait.\n\nErroneous code example:\n\n```compile_fail,E0277\n// here we declare the Foo trait with a bar method\ntrait Foo {\n    fn bar(&self);\n}\n\n// we now declare a function which takes an object implementing the Foo trait\nfn some_func<T: Foo>(foo: T) {\n    foo.bar();\n}\n\nfn main() {\n    // we now call the method with the i32 type, which doesn't implement\n    // the Foo trait\n    some_func(5i32); // error: the trait bound `i32 : Foo` is not satisfied\n}\n```\n\nIn order to fix this error, verify that the type you're using does implement\nthe trait. Example:\n\n```\ntrait Foo {\n    fn bar(&self);\n}\n\n// we implement the trait on the i32 type\nimpl Foo for i32 {\n    fn bar(&self) {}\n}\n\nfn some_func<T: Foo>(foo: T) {\n    foo.bar(); // we can now use this method since i32 implements the\n               // Foo trait\n}\n\nfn main() {\n    some_func(5i32); // ok!\n}\n```\n\nOr in a generic context, an erroneous code example would look like:\n\n```compile_fail,E0277\nfn some_func<T>(foo: T) {\n    println!(\"{:?}\", foo); // error: the trait `core::fmt::Debug` is not\n                           //        implemented for the type `T`\n}\n\nfn main() {\n    // We now call the method with the i32 type,\n    // which *does* implement the Debug trait.\n    some_func(5i32);\n}\n```\n\nNote that the error here is in the definition of the generic function. Although\nwe only call it with a parameter that does implement `Debug`, the compiler\nstill rejects the function. It must work with all possible input types. In\norder to make this example compile, we need to restrict the generic type we're\naccepting:\n\n```\nuse std::fmt;\n\n// Restrict the input type to types that implement Debug.\nfn some_func<T: fmt::Debug>(foo: T) {\n    println!(\"{:?}\", foo);\n}\n\nfn main() {\n    // Calling the method is still fine, as i32 implements Debug.\n    some_func(5i32);\n\n    // This would fail to compile now:\n    // struct WithoutDebug;\n    // some_func(WithoutDebug);\n}\n```\n\nRust only looks at the signature of the called function, as such it must\nalready specify all requirements that will be used for every type parameter.\n"},"level":"error","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.8.5\\src\\macros\\mod.rs","byte_start":26699,"byte_end":26792,"line_start":576,"line_end":576,"column_start":9,"column_end":102,"is_primary":true,"text":[{"text":"        $crate::sqlx_macros::expand_query!(record = $out_struct, source = $query, args = [$($args)*])","highlight_start":9,"highlight_end":102}],"label":"the trait `From<NaiveDateTime>` is not implemented for `chrono::DateTime<Utc>`","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.8.5\\src\\macros\\mod.rs","byte_start":26699,"byte_end":26792,"line_start":576,"line_end":576,"column_start":9,"column_end":102,"is_primary":false,"text":[{"text":"        $crate::sqlx_macros::expand_query!(record = $out_struct, source = $query, args = [$($args)*])","highlight_start":9,"highlight_end":102}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\db\\repositories\\sync_repository.rs","byte_start":16074,"byte_end":16390,"line_start":461,"line_end":468,"column_start":31,"column_end":14,"is_primary":false,"text":[{"text":"            let permissions = sqlx::query_as!(","highlight_start":31,"highlight_end":47},{"text":"                AgentPermission,","highlight_start":1,"highlight_end":33},{"text":"                r#\"","highlight_start":1,"highlight_end":20},{"text":"                SELECT id, agent_id, resource_type, resource_id, permission, granted_via, granted_via_id, created_at, updated_at","highlight_start":1,"highlight_end":129},{"text":"                FROM agent_permissions WHERE agent_id = $1","highlight_start":1,"highlight_end":59},{"text":"                \"#,","highlight_start":1,"highlight_end":20},{"text":"                agent_id","highlight_start":1,"highlight_end":25},{"text":"            )","highlight_start":1,"highlight_end":14}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"sqlx::query_as!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-0.8.5\\src\\macros\\mod.rs","byte_start":26477,"byte_end":26498,"line_start":571,"line_end":571,"column_start":1,"column_end":22,"is_primary":false,"text":[{"text":"macro_rules! query_as (","highlight_start":1,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::sqlx_macros::expand_query!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-1949cf8c6b5b557f\\sqlx-macros-0.8.5\\src\\lib.rs","byte_start":116,"byte_end":170,"line_start":9,"line_end":9,"column_start":1,"column_end":55,"is_primary":false,"text":[{"text":"pub fn expand_query(input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":55}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[{"message":"the following other types implement trait `From<T>`:\n  `chrono::DateTime<Utc>` implements `From<chrono::DateTime<FixedOffset>>`\n  `chrono::DateTime<Utc>` implements `From<chrono::DateTime<Local>>`\n  `chrono::DateTime<Utc>` implements `From<std::time::SystemTime>`","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"required for `NaiveDateTime` to implement `Into<chrono::DateTime<Utc>>`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0277]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: the trait bound `chrono::DateTime<Utc>: From<NaiveDateTime>` is not satisfied\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\db\\repositories\\sync_repository.rs:461:31\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m461\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m            let permissions = sqlx::query_as!(\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m _______________________________^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m462\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                AgentPermission,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m463\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                r#\"\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m464\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                SELECT id, agent_id, resource_type, resource_id, permission, granted_via, granted_via_id, created_at, updated_at\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m467\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                agent_id\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m468\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            )\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|_____________^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mthe trait `From<NaiveDateTime>` is not implemented for `chrono::DateTime<Utc>`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: the following other types implement trait `From<T>`:\u001b[0m\n\u001b[0m              `chrono::DateTime<Utc>` implements `From<chrono::DateTime<FixedOffset>>`\u001b[0m\n\u001b[0m              `chrono::DateTime<Utc>` implements `From<chrono::DateTime<Local>>`\u001b[0m\n\u001b[0m              `chrono::DateTime<Utc>` implements `From<std::time::SystemTime>`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: required for `NaiveDateTime` to implement `Into<chrono::DateTime<Utc>>`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: this error originates in the macro `$crate::sqlx_macros::expand_query` which comes from the expansion of the macro `sqlx::query_as` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no field `pool` on type `connection::Database`","code":{"code":"E0609","explanation":"Attempted to access a nonexistent field in a struct.\n\nErroneous code example:\n\n```compile_fail,E0609\nstruct StructWithFields {\n    x: u32,\n}\n\nlet s = StructWithFields { x: 0 };\nprintln!(\"{}\", s.foo); // error: no field `foo` on type `StructWithFields`\n```\n\nTo fix this error, check that you didn't misspell the field's name or that the\nfield actually exists. Example:\n\n```\nstruct StructWithFields {\n    x: u32,\n}\n\nlet s = StructWithFields { x: 0 };\nprintln!(\"{}\", s.x); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\db\\repositories\\sync_repository.rs","byte_start":16423,"byte_end":16427,"line_start":469,"line_end":469,"column_start":33,"column_end":37,"is_primary":true,"text":[{"text":"            .fetch_all(&self.db.pool)","highlight_start":33,"highlight_end":37}],"label":"unknown field","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"available field is: `connection`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0609]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: no field `pool` on type `connection::Database`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\db\\repositories\\sync_repository.rs:469:33\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m469\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            .fetch_all(&self.db.pool)\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9munknown field\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: available field is: `connection`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no field `pool` on type `connection::Database`","code":{"code":"E0609","explanation":"Attempted to access a nonexistent field in a struct.\n\nErroneous code example:\n\n```compile_fail,E0609\nstruct StructWithFields {\n    x: u32,\n}\n\nlet s = StructWithFields { x: 0 };\nprintln!(\"{}\", s.foo); // error: no field `foo` on type `StructWithFields`\n```\n\nTo fix this error, check that you didn't misspell the field's name or that the\nfield actually exists. Example:\n\n```\nstruct StructWithFields {\n    x: u32,\n}\n\nlet s = StructWithFields { x: 0 };\nprintln!(\"{}\", s.x); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\db\\repositories\\sync_repository.rs","byte_start":17278,"byte_end":17282,"line_start":499,"line_end":499,"column_start":31,"column_end":35,"is_primary":true,"text":[{"text":"            .execute(&self.db.pool)","highlight_start":31,"highlight_end":35}],"label":"unknown field","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"available field is: `connection`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0609]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: no field `pool` on type `connection::Database`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\db\\repositories\\sync_repository.rs:499:31\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m499\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            .execute(&self.db.pool)\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                               \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9munknown field\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: available field is: `connection`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no field `pool` on type `connection::Database`","code":{"code":"E0609","explanation":"Attempted to access a nonexistent field in a struct.\n\nErroneous code example:\n\n```compile_fail,E0609\nstruct StructWithFields {\n    x: u32,\n}\n\nlet s = StructWithFields { x: 0 };\nprintln!(\"{}\", s.foo); // error: no field `foo` on type `StructWithFields`\n```\n\nTo fix this error, check that you didn't misspell the field's name or that the\nfield actually exists. Example:\n\n```\nstruct StructWithFields {\n    x: u32,\n}\n\nlet s = StructWithFields { x: 0 };\nprintln!(\"{}\", s.x); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\db\\repositories\\sync_repository.rs","byte_start":17946,"byte_end":17950,"line_start":524,"line_end":524,"column_start":31,"column_end":35,"is_primary":true,"text":[{"text":"            .execute(&self.db.pool)","highlight_start":31,"highlight_end":35}],"label":"unknown field","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"available field is: `connection`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0609]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: no field `pool` on type `connection::Database`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\db\\repositories\\sync_repository.rs:524:31\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m524\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            .execute(&self.db.pool)\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                               \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9munknown field\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: available field is: `connection`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no field `pool` on type `connection::Database`","code":{"code":"E0609","explanation":"Attempted to access a nonexistent field in a struct.\n\nErroneous code example:\n\n```compile_fail,E0609\nstruct StructWithFields {\n    x: u32,\n}\n\nlet s = StructWithFields { x: 0 };\nprintln!(\"{}\", s.foo); // error: no field `foo` on type `StructWithFields`\n```\n\nTo fix this error, check that you didn't misspell the field's name or that the\nfield actually exists. Example:\n\n```\nstruct StructWithFields {\n    x: u32,\n}\n\nlet s = StructWithFields { x: 0 };\nprintln!(\"{}\", s.x); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\db\\repositories\\sync_repository.rs","byte_start":18168,"byte_end":18172,"line_start":531,"line_end":531,"column_start":31,"column_end":35,"is_primary":true,"text":[{"text":"            .execute(&self.db.pool)","highlight_start":31,"highlight_end":35}],"label":"unknown field","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"available field is: `connection`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0609]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: no field `pool` on type `connection::Database`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\db\\repositories\\sync_repository.rs:531:31\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m531\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            .execute(&self.db.pool)\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                               \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9munknown field\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: available field is: `connection`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no field `pool` on type `connection::Database`","code":{"code":"E0609","explanation":"Attempted to access a nonexistent field in a struct.\n\nErroneous code example:\n\n```compile_fail,E0609\nstruct StructWithFields {\n    x: u32,\n}\n\nlet s = StructWithFields { x: 0 };\nprintln!(\"{}\", s.foo); // error: no field `foo` on type `StructWithFields`\n```\n\nTo fix this error, check that you didn't misspell the field's name or that the\nfield actually exists. Example:\n\n```\nstruct StructWithFields {\n    x: u32,\n}\n\nlet s = StructWithFields { x: 0 };\nprintln!(\"{}\", s.x); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\db\\repositories\\sync_repository.rs","byte_start":19006,"byte_end":19010,"line_start":561,"line_end":561,"column_start":33,"column_end":37,"is_primary":true,"text":[{"text":"            .fetch_one(&self.db.pool)","highlight_start":33,"highlight_end":37}],"label":"unknown field","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"available field is: `connection`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0609]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: no field `pool` on type `connection::Database`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\db\\repositories\\sync_repository.rs:561:33\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m561\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            .fetch_one(&self.db.pool)\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9munknown field\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: available field is: `connection`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no field `pool` on type `connection::Database`","code":{"code":"E0609","explanation":"Attempted to access a nonexistent field in a struct.\n\nErroneous code example:\n\n```compile_fail,E0609\nstruct StructWithFields {\n    x: u32,\n}\n\nlet s = StructWithFields { x: 0 };\nprintln!(\"{}\", s.foo); // error: no field `foo` on type `StructWithFields`\n```\n\nTo fix this error, check that you didn't misspell the field's name or that the\nfield actually exists. Example:\n\n```\nstruct StructWithFields {\n    x: u32,\n}\n\nlet s = StructWithFields { x: 0 };\nprintln!(\"{}\", s.x); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\db\\repositories\\sync_repository.rs","byte_start":19203,"byte_end":19207,"line_start":567,"line_end":567,"column_start":33,"column_end":37,"is_primary":true,"text":[{"text":"            .fetch_one(&self.db.pool)","highlight_start":33,"highlight_end":37}],"label":"unknown field","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"available field is: `connection`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0609]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: no field `pool` on type `connection::Database`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\db\\repositories\\sync_repository.rs:567:33\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m567\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            .fetch_one(&self.db.pool)\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9munknown field\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: available field is: `connection`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no field `pool` on type `connection::Database`","code":{"code":"E0609","explanation":"Attempted to access a nonexistent field in a struct.\n\nErroneous code example:\n\n```compile_fail,E0609\nstruct StructWithFields {\n    x: u32,\n}\n\nlet s = StructWithFields { x: 0 };\nprintln!(\"{}\", s.foo); // error: no field `foo` on type `StructWithFields`\n```\n\nTo fix this error, check that you didn't misspell the field's name or that the\nfield actually exists. Example:\n\n```\nstruct StructWithFields {\n    x: u32,\n}\n\nlet s = StructWithFields { x: 0 };\nprintln!(\"{}\", s.x); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\db\\repositories\\sync_repository.rs","byte_start":19402,"byte_end":19406,"line_start":573,"line_end":573,"column_start":33,"column_end":37,"is_primary":true,"text":[{"text":"            .fetch_one(&self.db.pool)","highlight_start":33,"highlight_end":37}],"label":"unknown field","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"available field is: `connection`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0609]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: no field `pool` on type `connection::Database`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\db\\repositories\\sync_repository.rs:573:33\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m573\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            .fetch_one(&self.db.pool)\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9munknown field\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: available field is: `connection`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `owner_role_id`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\db\\seed.rs","byte_start":5782,"byte_end":5795,"line_start":158,"line_end":158,"column_start":5,"column_end":18,"is_primary":true,"text":[{"text":"    owner_role_id: &Uuid,","highlight_start":5,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_variables)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\db\\seed.rs","byte_start":5782,"byte_end":5795,"line_start":158,"line_end":158,"column_start":5,"column_end":18,"is_primary":true,"text":[{"text":"    owner_role_id: &Uuid,","highlight_start":5,"highlight_end":18}],"label":null,"suggested_replacement":"_owner_role_id","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `owner_role_id`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\db\\seed.rs:158:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m158\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    owner_role_id: &Uuid,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_owner_role_id`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(unused_variables)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `member_role_id`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\db\\seed.rs","byte_start":5809,"byte_end":5823,"line_start":159,"line_end":159,"column_start":5,"column_end":19,"is_primary":true,"text":[{"text":"    member_role_id: &Uuid,","highlight_start":5,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\db\\seed.rs","byte_start":5809,"byte_end":5823,"line_start":159,"line_end":159,"column_start":5,"column_end":19,"is_primary":true,"text":[{"text":"    member_role_id: &Uuid,","highlight_start":5,"highlight_end":19}],"label":null,"suggested_replacement":"_member_role_id","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `member_role_id`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\db\\seed.rs:159:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m159\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    member_role_id: &Uuid,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_member_role_id`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"aborting due to 29 previous errors; 4 warnings emitted","code":null,"level":"error","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: aborting due to 29 previous errors; 4 warnings emitted\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"Some errors have detailed explanations: E0277, E0609.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;15mSome errors have detailed explanations: E0277, E0609.\u001b[0m\n"}
{"$message_type":"diagnostic","message":"For more information about an error, try `rustc --explain E0277`.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;15mFor more information about an error, try `rustc --explain E0277`.\u001b[0m\n"}
